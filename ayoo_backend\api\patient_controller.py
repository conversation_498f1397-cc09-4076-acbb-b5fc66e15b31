import pymongo
from .DAOs.caseSheetDAO import CaseSheetDAO
from fastapi import HTTPException
from sqlalchemy import or_, and_, func
import random
import string
import uuid
import datetime
import copy
import enum
from typing import Optional
import time
from dateutil.relativedelta import relativedelta
from collections import defaultdict

from .DAOs.appointmentDAO import Appointment<PERSON><PERSON>, CaseSheetQueryFields
import sqlalchemy.exc
from sqlalchemy.orm import relationship, scoped_session
from sqlalchemy import or_
from . import dbmodels
from .chat_controller import ChatController
from .text_local_service.text_local_controller import TextLocalController

from .views import logger, loggers

from .api_configs import OTP_GENERATOR, URL_SHORTENER_PATH, current_env, WEB_URL_PATH
from .aws_msg_email_generator import AWSEmailAndMsgSender
from .ayoo_utils import phone_number_parser, encrypt_password, get_age_in_years, check_minor, \
    calcualte_appointment_duration
from .mental_health_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>ontroller
from .view_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>er, Ad<PERSON><PERSON><PERSON>roller
from .doctor_controller import Doctor<PERSON><PERSON>roller
from .relatives_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>roll<PERSON>
from .guest_controller import Guest<PERSON>ontroller
from .dbmodels import DBBlockSlots, DBDataAccess, DBFamilies, DBFamilyCode, DBFamilyUserMapping, \
    DBFamilyUsers, \
    DBRelations, DBUser, DBGuest, DBRelatives, DBDoctor, DBClinic, DBClinicAndDoctors, DBRBAC, DBCaretakerAccessCode
from .doctormodels import RequestAppointmentList, ResponseAppointmentList, \
    RequestDoctorsVirtualAvailableSlots
from .patient_models import AddFamilyIDRelative, AddFamilyIDRelativeResponse, AddFamilyResponse, \
    AddFamilyView, \
    AddMemberToFamilyView, AddRelationResp, AddRelativeToFamilyView, AdminAddRelativeToFamilyView, \
    AdminGetUserFamilyRequest, AdminRemoveFamilyMemberRequest, AppointmentBooking, AppointmentBookingByAdmin, \
    AppointmentBookingDetailsCopy, AppointmentBookingDetailsCopy1, AppointmentBookingResponse, CreateNewFamilyResponse, \
    CreateNewFamilyView, CreateNewRelativeResponse, DataAccessResponse, DataAccessView, FamilyCodeGen, \
    GetAppointmentView, GetRelativeToken, PatientRelationType, \
    AppointmentBookingDetails, AppointmentBookingForOthers, GuestAppointmentBooking, GuestAppointmentBookingForOthers, \
    PrimaryMemberPolicyData, RelativePermData, RemoveFamilyMemberRequest, \
    ResponseMemberAppointmentList, SearchClinicView, PatientHealthHistoryView, GetPatientHealthHistoryView, ReportView, \
    GetPatientReportView, SlotBlockingResponse, SlotBlockingView, UpdateReportRequestView, DeleteReportRequestView, \
    PatientFeedbackForDoctor, \
    PatientFeedbackForDoctorResponse, GetDoctorFeedback, SearchAllClinicView, SearchClinicIdView, \
    ResponseMemberAppointmentListCopy, CheckDoctorAvailableSlot, AppointmentType, PatientVitalsInfo, \
    PatientVitalsInfoUserId, UploadPrescriptionRequestView, PrescriptionView, UpdatePrescriptionRequestView, \
    DeletePrescriptionRequestView, PatientVitalsInfoDate, PatientVitalsInfoDateReadingId, PatientHealthHistoryViewUser, \
    UploadPatientProfileImages, PatientProfileImageView, UploadPatientProfileImagesByAdmin, DoctorAppointmentBooking, \
    MentalHealthSelfAppointmentBooking, MentalHealthAppointmentBookingResponse, \
    MentalHealthOthersAppointmentBooking, \
    MentalHealthAppointmentBookingDetails, MentalHealthGroupAppointmentBooking, RelativeData, \
    MentalHealthPatientRelationType, NotificationPushMessage, AppDownloadLink, AppointmentConfirmationToAdmin, \
    UpdateAppointmentStatus, appointmentstatus, appointmentstatusdel, \
    appointmentreason, appointmentreasondel, PatientsRelatives, AppointmentCancellationModel, CaretakerAccessCodeGen, \
    ConsentRecord, DeleteFamilyRelation, LogoutRequest, PatientProfileMerge
from .google_api import get_distance_lat_lon
import calendar
from .aws_s3 import AWSS3Client
from .jitsi_meet import JitsiMeetController
from .viewmodels import CreateUserByAdmin, \
    AddFamilyMember, ApproveFamilyMemberAddition, DeleteFamilyRelation, UpdateFamilyMember, EmergencyContactDetails

from .firebase_controller import FireBaseNotificationController
from .firebase_models import AppointmentEvent, FamilyMemberAddEvent, FamilyDoctorEvent, \
    MentalHealthGroupAppointmentEvent, MentalHealthOthersAppointmentEvent, FamilyMemberRequestEvent
import pyshorteners
from .DAOs.paymentDAO import CustomFeesDAO
from ayoo_backend.api.DAOs.caseSheetDAO import CaseSheetDAO
from ayoo_backend.api.database import get_db

class PatientController:

    def __init__(self, db: scoped_session, mongo=None):
        self.db = db
        self.mongo = mongo
        self.mongo_db = self.mongo['ayoo']
        self.otp_generator = OTP_GENERATOR

    def __generate_case_id(self):
        mh_ctrl = MentalHealthController(db=self.db, mongo=self.mongo)
        case_id = mh_ctrl.generate_case_id()
        return case_id

    def __available_appointment_slot(self, appointment_slot, doctor_id: str):
        mongo_collection_check_slots = self.mongo_db['Appointments']
        check_slot = mongo_collection_check_slots.find_one(
            {
                'appointment_slot': appointment_slot,
                '$and': [
                    {'is_active': True},
                    {'is_confirmed': True},
                    {'doctorid': doctor_id}
                ]
            }
        )

        if check_slot:
            return False
        else:
            return True

    def __check_patients_availablity_in_appointments(self, patients, appointment_slot):
        mongo_collection_check_slots = self.mongo_db['Appointments']
        for patient in patients:
            check_patient_availablity = mongo_collection_check_slots.find_one(
                {
                    'appointment_slot': appointment_slot,
                    '$and': [
                        {'patient_id': patient},
                        {'is_active': True},
                        {'is_confirmed': True}
                    ]
                }
            )
            if check_patient_availablity:
                return False
            else:
                return True

    def __get_patient_details(self, patientid: str):
        from ayoo_backend.api import dbmodels

        patient_data = {}
        resp_user: DBUser = self.db.query(dbmodels.DBUser).filter_by(
            userid=patientid, is_deleted=False).one_or_none()
        if resp_user:
            patient_data['ayoo_id'] = resp_user.ayoo_id
            patient_data['userid'] = resp_user.userid
            patient_data['firstname'] = resp_user.firstname
            patient_data['lastname'] = resp_user.lastname
            patient_data['dob'] = resp_user.birthdate
            patient_data['email'] = resp_user.email
            patient_data['mobile'] = resp_user.mobile
            patient_data['gender'] = resp_user.gender
            patient_data['caretaker_id'] = ""

            return patient_data

        resp_relative: DBRelatives = self.db.query(dbmodels.DBRelatives).filter(
            DBRelatives.relativeid == patientid).first()
        if resp_relative:
            patient_data['ayoo_id'] = resp_relative.ayoo_id
            patient_data['userid'] = resp_relative.relativeid
            patient_data['firstname'] = resp_relative.firstname
            patient_data['lastname'] = resp_relative.lastname
            patient_data['dob'] = resp_relative.birthdate
            patient_data['email'] = resp_relative.email
            patient_data['mobile'] = resp_relative.mobile
            patient_data['gender'] = resp_relative.gender
            patient_data['caretaker_id'] = resp_relative.caretaker_id

            return patient_data
        else:
            return None

    def __get_clinic_details(self, clinicid: str):
        try:
            resp = self.db.query(DBClinic).filter_by(clinicid=str(clinicid)).one_or_none()
            if resp:
                return resp.name
            else:
                return None
        except Exception as e:
            return None, f'Internal Error code {str(e)} for getting clinic with id: {clinicid}'

    def get_user_profile_image(self, user_id: str):
        try:
            check_info_exist = self.mongo_db['UserCollection'].find_one({"userid": str(user_id)})
            if check_info_exist and 'profile_image_info' in check_info_exist:
                return check_info_exist['profile_image_info']['profile_image_url'], 'user profile image found'
            else:
                return None, 'user profile image not found'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while getting user profile image for userid: {user_id}'

    def upload_user_profile_image(self, profile_view: UploadPatientProfileImages, user_id: str):
        try:
            s3_instance = AWSS3Client()

            check_user_image_exist, msg = self.get_user_profile_image(user_id=str(user_id))

            if check_user_image_exist is not None:
                s3_patient_profile_pic_url = check_user_image_exist.split('/')
                image_key = f'patient/{user_id}/images/{s3_patient_profile_pic_url[-1]}'
                s3_instance.delete_object_from_s3(image_key=image_key)

            image_id = str(uuid.uuid4())
            generated_url, msg = s3_instance.upload_patient_profile_iamge_to_s3(
                image_str=str(profile_view.profile_image_encoded), image_id=str(image_id), patient_id=user_id)
            if generated_url is None:
                return None, msg
            r1: DBUser = self.db.query(dbmodels.DBUser).filter(DBUser.userid == user_id).one_or_none()
            if r1 is None:
                return None, f'{user_id} does not exist'
            profile_image_info = dict(
                image_id=image_id,
                profile_image_url=generated_url
            )

            check_user_dict_exists = self.mongo_db['UserCollection'].find_one({"userid": str(user_id)})
            if check_user_dict_exists:
                self.mongo_db['UserCollection'].find_one_and_update({"userid": str(user_id)}, {
                    "$set": {'profile_image_info': profile_image_info}})
            else:
                self.mongo_db['UserCollection'].insert_one(dict(userid=user_id, profile_image_info=profile_image_info))

            user_ctrl = UserController(db=self.db, otp_generator=None)
            updated_user_details = user_ctrl.get_user_details(userid=user_id, mongo=self.mongo)

            return updated_user_details, 'successfully uploaded'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while uploading the user profile image'

    def delete_user_profile_image(self, user_id: str):
        try:
            check_user_image_exist, msg = self.get_user_profile_image(user_id=str(user_id))
            if check_user_image_exist is not None:

                s3_patient_profile_pic_url = check_user_image_exist.split('/')
                image_key = f'patient/{user_id}/images/{s3_patient_profile_pic_url[-1]}'

                s3_instance = AWSS3Client()
                s3_instance.delete_object_from_s3(image_key=image_key)

                self.mongo_db['UserCollection'].find_one_and_update(
                    {"userid": str(user_id)}, {"$set": {'profile_image_info': {
                        "image_id": None, "profile_image_url": None}}}
                )
                user_ctrl = UserController(db=self.db, otp_generator=None)
                updated_user_details = user_ctrl.get_user_details(userid=user_id, mongo=self.mongo)
                return updated_user_details, 'successfully deleted'
            else:
                return None, f'profile image not found for userid: {str(user_id)}'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while deleting the user profile image'

    def change_new_user_to_old_user(self, user_id):
        try:
            user_mongo_data = self.mongo_db['UserCollection'].find_one({'userid': user_id})

            if user_mongo_data is None:
                self.mongo_db['UserCollection'].insert_one(
                    dict(
                        userid=user_id,
                        new_user=False
                    )
                )
            else:
                self.mongo_db['UserCollection'].find_one_and_update({'userid': user_id},
                                                                    {'$set': {'new_user': False}})
        except Exception as e:
            raise Exception(str(e))

    def send_appointment_confirmation_mail_to_admin(self, appointment_details: AppointmentBookingDetails,
                                                    other_info: AppointmentConfirmationToAdmin):
        try:
            # from api_configs import current_env
            logger.info(current_env)
            if current_env.rstrip() != 'prod':
                logger.info("The current env is {}".format(current_env))
                return
            logger.info("Current Env {}".format(current_env))
            aws_mail_ctrl = AWSEmailAndMsgSender()
            text_local_controller = TextLocalController()

            ayoo_admin: dbmodels.DBAdmin = self.db.query(dbmodels.DBAdmin).filter(
                dbmodels.DBAdmin.email == '<EMAIL>').one_or_none()
            logger.info(ayoo_admin)
            booked_by = 'Admin' if appointment_details.booked_by == ayoo_admin.userid else 'Self'
            fees_info = self.mongo_db['Paymentgateway3'].find_one(
                dict(appointment_id=appointment_details.appointment_id), {'amount': 1})
            fees = fees_info if fees_info is not None else other_info.doctor_details[
                'consulting_fees_clinic'] if appointment_details.appointment_type == 'InClinic' else \
                other_info.doctor_details['consulting_fees_virtual']

            booked_for = appointment_details.appointment_for.name if appointment_details.appointment_for.name == 'Self' else f'{other_info.patient_name} {(appointment_details.appointment_for.name)}'

            client_name = other_info.patient_name if other_info.booking_person_name == '' else other_info.booking_person_name

            appointment_status = 'rescheduled' if other_info.is_rescheduled_appointment is True else 'confirmed'

            msg_for_admin = f'''
Dear Admin,
We're excited to share news of a {appointment_status} booking for a consultation on our platform.

The details are as follows:
- Client's Name: {client_name}
- Session Date: {other_info.appointment_date}
- Session Time: {other_info.appointment_time}
- Provider: {other_info.doctor_name}
- Appointment Type: {str(appointment_details.appointment_type.name)}
- Total Cost: {fees}
- Booking Timestamp: {appointment_details.created_at}
- Booked By: {booked_by}
- Booked For : {booked_for}

This reservation reflects our commitment to facilitating wellness journeys. Your role in coordinating these sessions is vital. As the admin, your involvement ensures a seamless experience for both the user and the provider.

Please review these details and liaise with the involved parties, if necessary, to ensure everything proceeds smoothly. Your prompt communication can make all the difference in providing a positive and effective consultation.

Feel free to reach out if you need any clarifications or assistance. Your dedication to our users' well-being is greatly appreciated.

Best regards,
Engineering Team
            '''
            phone_text_for_admin = f'Appointment {appointment_status} for {other_info.patient_name} on {other_info.appointment_date} at {other_info.appointment_time} with {other_info.doctor_name}'

            email_subject = f'{appointment_status.title()} Consultation Booking: {other_info.patient_name}- {other_info.appointment_date}, {other_info.appointment_time}'
            logger.info(phone_text_for_admin)
            logger.info(email_subject)
            aws_mail_ctrl.send_appointment_confirmation(meeting_message=msg_for_admin,
                                                        mobile=ayoo_admin.mobile,
                                                        email=ayoo_admin.email,
                                                        subject=email_subject,
                                                        phone_text_for_admin=phone_text_for_admin)

            appointment_type = str(appointment_details.appointment_type.name)
            if other_info.is_rescheduled_appointment is True:
                if appointment_type == 'Virtual':
                    text_local_controller.send_sms(template_name=f'AppointmentRescheduleAdminVirtual',
                                                   var_list=[other_info.patient_name, other_info.appointment_date,
                                                             other_info.appointment_time, other_info.doctor_name],
                                                   numbers=ayoo_admin.mobile)
                else:
                    text_local_controller.send_sms(template_name=f'AppointmentRescheduleAdminInClinic', var_list=[
                        f'{other_info.appointment_date}, {other_info.appointment_time}', other_info.doctor_name],
                                                   numbers=ayoo_admin.mobile)

            else:
                if appointment_type == 'Virtual':
                    text_local_controller.send_sms(template_name=f'AppointmentBookAdminVirtual',
                                                   var_list=[other_info.patient_name,
                                                             f'{other_info.appointment_date}, {other_info.appointment_time}',
                                                             other_info.doctor_name], numbers=ayoo_admin.mobile)
                else:
                    text_local_controller.send_sms(template_name=f'AppointmentBookAdminInClinic',
                                                   var_list=[other_info.patient_name, other_info.appointment_date,
                                                             other_info.appointment_time, other_info.doctor_name],
                                                   numbers=ayoo_admin.mobile)

        except Exception as e:
            print(str(e))

    def manage_notifications_for_booked_appointments(self, appointment_id: str,
                                                     is_rescheduled_appointment: bool = False,
                                                     previous_appointment_id: str = None):
        try:
            loggers['logger6'].info(
                f'Creating notifications for appointment in notifications collection. Appointment id:{appointment_id}')

            if is_rescheduled_appointment:
                self.mongo_db['Notifications'].update_many({'object_id': previous_appointment_id}, {'$set': {
                    'object_active_status': False
                }})

            created_at = datetime.datetime.now()
            formatted_date_time = created_at.strftime("%Y-%m-%d %H:%M:%S")
            date_time_now = datetime.datetime.strptime(formatted_date_time, "%Y-%m-%d %H:%M:%S")
            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            appointment_data = self.mongo_db['Appointments'].find_one({'appointment_id': appointment_id})
            if appointment_data is None:
                return None, 'Invalid appointment ID'

            appointment_details = AppointmentBookingDetails(
                appointment_id=appointment_id,
                appointment_type=appointment_data['appointment_type'],
                appointment_for=appointment_data['appointment_for'],
                children_included=appointment_data['children_included'],
                symptoms=appointment_data['symptoms'],
                symptoms_audio_clip=appointment_data['symptoms_audio_clip'],
                additional_notes=appointment_data.get('additional_notes', None),
                clinicid=appointment_data['clinicid'],
                doctorid=appointment_data['doctorid'],
                appointment_slot=appointment_data['appointment_slot'],
                is_active=appointment_data['is_active'],
                is_confirmed=appointment_data['is_confirmed'],
                payment=appointment_data['payment'],
                caseid=appointment_data['caseid'],
                patient_id=appointment_data['patient_id'],
                patients=appointment_data['patients'],
                booked_by=appointment_data['booked_by'],
                created_at=appointment_data['created_at'],
                end_date=appointment_data['end_date'],
                is_first_appointment=appointment_data['is_first_appointment'],
                case_open=appointment_data['case_open'],
                follow_up_type=appointment_data['follow_up_type'],
                care_type=appointment_data['care_type']
            )

            slot_duration = calcualte_appointment_duration(
                (appointment_details.end_date - appointment_details.appointment_slot).total_seconds() / 60)

            jitsi_link = 'None'
            doctor_meeting_link_info = None
            doctor_meeting_code = None
            patient_meeting_link_info = None
            patient_meeting_code = None

            if appointment_details.appointment_type == 'Virtual':
                jitsi_ctrl = JitsiMeetController(db=self.db, mongo=self.mongo)
                meeting_info = jitsi_ctrl.check_meeting_using_appointment_id(
                    appointment_id=str(appointment_id))
                if meeting_info is None:
                    return None, 'Could not find meeting details'

                jitsi_link = meeting_info['meeting_link']
                patient_meeting_link_info = meeting_info[
                    'patient_joining_link'] if 'patient_joining_link' in meeting_info else None
                patient_meeting_code = meeting_info['meeting_code'] if 'meeting_code' in meeting_info else None

                doctor_meeting_link_info = meeting_info[
                    'doctor_joining_link'] if 'doctor_joining_link' in meeting_info else None
                doctor_meeting_code = meeting_info[
                    'doctor_meeting_code'] if 'doctor_meeting_code' in meeting_info else None

            patient_id = appointment_details.patient_id
            patient_details = self.__get_patient_details(patientid=patient_id)
            patient_name = ''
            if patient_details:
                patient_name = patient_details['firstname'] + ' ' + patient_details['lastname']
            service_provider = []
            doctor_details, msg = doctor_ctrl.get_by_id(doctorid=appointment_details.doctorid)
            if doctor_details is not None:
                service_provider = {
                    'doctor_firstname': doctor_details['firstname'],
                    'doctor_lastname': doctor_details['lastname'],
                    'graduation': doctor_details['graduation'],
                    'masters': doctor_details['masters'],
                    'specialization': doctor_details['specialization'],
                    'specialization_field': doctor_details['specialization_field']
                }

            if appointment_details.appointment_for == 'Self':
                user_id = patient_id
            else:
                check_if_booked_by_admin = self.mongo_db['AdminCollection'].find_one(
                    {'admin_id': appointment_details.booked_by})
                if check_if_booked_by_admin is not None:
                    user_id = patient_id
                else:
                    caretaker_details = self.db.query(DBUser).filter(
                        DBUser.userid == str(appointment_details.booked_by)).one_or_none()
                    if caretaker_details is None:
                        user_id = patient_id
                    else:
                        user_id = str(appointment_details.booked_by)

            frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)
            appointment_notif = AppointmentEvent(appointment_id=appointment_id,
                                                 user_id=user_id,
                                                 user_name=self.__get_patient_details(patientid=user_id)['firstname'],
                                                 appointment_for=appointment_details.appointment_for,
                                                 patient_id=patient_id,
                                                 patient_name=patient_name,
                                                 doctor_id=appointment_details.doctorid,
                                                 doctor_name=service_provider['doctor_firstname'],
                                                 clinic_id=appointment_details.clinicid,
                                                 clinic_name=self.__get_clinic_details(appointment_details.clinicid),
                                                 event_date=appointment_details.appointment_slot,
                                                 event_type=appointment_details.appointment_type,
                                                 jitsi_link=jitsi_link)

            clinic = {}
            if appointment_details.clinicid:
                clinic_details = doctor_ctrl.get_clinic_by_id(
                    clinicid=appointment_details.clinicid)
                if clinic_details:
                    clinic = dict(
                        clinic_id=clinic_details.clinicid,
                        clinic_name=clinic_details.name,
                        clince_mobile=clinic_details.mobile,
                        clinic_starts_at=clinic_details.starts_at,
                        clinic_ends_at=clinic_details.ends_at,
                        clinic_address=clinic_details.address,
                        lat=clinic_details.lat,
                        lon=clinic_details.lon
                    )

            doctor_data = {}
            if doctor_details is not None:
                doctor_data = {
                    'name': doctor_details['firstname'] + ' ' + doctor_details['lastname'],
                    'languages': doctor_details['languages'],
                    'graduation': doctor_details['graduation'],
                    'masters': doctor_details['masters'],
                    'doctortype': doctor_details['doctortype'],
                    'specialization': doctor_details['specialization'],
                    'additional_qualification': doctor_details['additional_qualification'],
                    'bio': doctor_details['bio'],
                    'consulting_duration_virtual': doctor_details['consulting_duration_virtual'],
                    'consulting_duration_clinic': doctor_details['consulting_duration_clinic'],
                    'consulting_fees_virtual': doctor_details['consulting_fees_virtual'],
                    'consulting_fees_clinic': doctor_details['consulting_fees_clinic'],
                    'image_id': doctor_details['image_id'],
                    'profile_image_url': doctor_details['profile_image_url']
                }

            patient_details['dob'] = str(patient_details['dob'])
            notification_route_data_for_patient = NotificationPushMessage(
                appointment_id=appointment_id,
                caseid=appointment_details.caseid,
                symptoms=appointment_details.symptoms,
                symptoms_audio_clip=appointment_details.symptoms_audio_clip,
                additional_notes=appointment_details.additional_notes,
                appointment_slot=appointment_details.appointment_slot.strftime("%Y-%m-%dT%H:%M:%S"),
                appointment_type=appointment_details.appointment_type,
                appointment_for=appointment_details.appointment_for,
                patient=patient_details,
                clinic=clinic,
                doctor=doctor_data,
                meeting_link=patient_meeting_link_info,
                meeting_code=patient_meeting_code
            )

            notification_route_data_for_doctor = NotificationPushMessage(
                appointment_id=appointment_id,
                caseid=appointment_details.caseid,
                symptoms=appointment_details.symptoms,
                symptoms_audio_clip=appointment_details.symptoms_audio_clip,
                additional_notes=appointment_details.additional_notes,
                appointment_slot=appointment_details.appointment_slot.strftime("%Y-%m-%dT%H:%M:%S"),
                appointment_type=appointment_details.appointment_type,
                appointment_for=appointment_details.appointment_for,
                patient=patient_details,
                clinic=clinic,
                doctor=doctor_data,
                meeting_link=doctor_meeting_link_info,
                meeting_code=doctor_meeting_code
            )

            notification_route_data_for_care_taker = None
            # if appointment_details.appointment_for != 'Self':
            if appointment_details.appointment_for != 'Self' and patient_id != user_id:
                notification_route_data_for_care_taker = NotificationPushMessage(
                    appointment_id=appointment_details.appointment_id,
                    caseid=appointment_details.caseid,
                    symptoms=appointment_details.symptoms,
                    symptoms_audio_clip=appointment_details.symptoms_audio_clip,
                    additional_notes=appointment_details.additional_notes,
                    appointment_slot=appointment_details.appointment_slot.strftime("%Y-%m-%dT%H:%M:%S"),
                    appointment_type=appointment_details.appointment_type,
                    appointment_for=appointment_details.appointment_for,
                    patient=patient_details,
                    clinic=clinic,
                    doctor=doctor_data,
                    meeting_link=None,
                    meeting_code=None
                )

            frbs_ctrl.appointment_booking_notifs(appointment_event=appointment_notif,
                                                 data_for_patient=notification_route_data_for_patient,
                                                 data_for_doctor=notification_route_data_for_doctor,
                                                 data_for_care_taker=notification_route_data_for_care_taker,
                                                 is_rescheduled_appointment=is_rescheduled_appointment)

            # send email
            ctrl = UserController(db=self.db, otp_generator=OTP_GENERATOR)
            text_local_controller = TextLocalController()

            aws_mail_ctrl = AWSEmailAndMsgSender()

            appointment_date = appointment_details.appointment_slot.strftime("%d %B %Y")
            appointment_time = appointment_details.appointment_slot.strftime("%I:%M %p")

            doctor_name = service_provider['doctor_firstname'] + ' ' + service_provider['doctor_lastname']
            doctor_specialization = service_provider['specialization']

            booked_by_email = None
            booked_by_mobile = None
            booked_by_name = ''
            msg_for_appointment_booking_person = ''
            template_name_for_booked_by_person = f'AppointmentBookBookedByPerson{appointment_details.appointment_type}'
            var_list_booked_by_person = []
            appointment_status = 'rescheduled' if is_rescheduled_appointment is True else 'confirmed'

            # if appointment_details.appointment_for != 'Self':
            if appointment_details.appointment_for != 'Self' and patient_id != user_id:
                booked_by_details = ctrl.get_user_details(userid=appointment_details.booked_by, mongo=self.mongo)
                booked_by_email = booked_by_details['email']
                booked_by_mobile = booked_by_details['mobile']
                booked_by_name = booked_by_details['firstname'] + ' ' + booked_by_details['lastname']
                msg_for_appointment_booking_person = f'The video consultation you booked for {patient_name} with {doctor_name} ({doctor_specialization}) is {appointment_status} for {int(slot_duration)} minutes on {appointment_date} at {appointment_time}. {patient_name} will receive an OTP 30 minutes before the start of your consultation.'
                var_list_booked_by_person = [patient_name, doctor_name, f'{appointment_date}, {appointment_time}',
                                             slot_duration]

            # patient_gender = patient_details['gender']
            # patient_dob = datetime.datetime.strptime(patient_details['dob'], "%Y-%m-%d")
            #
            # current_date = datetime.datetime.today()
            # patient_age = current_date.year - patient_dob.year
            # if current_date.month < patient_dob.month or (
            #         current_date.month == patient_dob.month and current_date.day < patient_dob.day):
            #     patient_age -= 1

            template_name_for_patient = f'AppointmentBookPatientVirtualBefore30MinBySelf'
            var_list_patient = [doctor_name, appointment_date, appointment_time, slot_duration]

            if is_rescheduled_appointment is True:
                template_name_for_patient = 'AppointmentReschedulePatientVirtualBefore30MinBySelf'
                var_list_patient = [doctor_name, slot_duration, appointment_date, appointment_time]

            msg_for_patient = f'Your video consultation with {doctor_name} ({doctor_specialization}) is {appointment_status} for {int(slot_duration)} minutes on {appointment_date} at {appointment_time}. You will receive an OTP 30 minutes before the start of your consultation.'

            # if appointment_details.appointment_for != 'Self':
            if appointment_details.appointment_for != 'Self' and patient_id != user_id:
                msg_for_patient = f'We confirm your video consultation booked by {booked_by_name} with {doctor_name} ({doctor_specialization}) for {int(slot_duration)} minutes on {appointment_date} at {appointment_time}. You will receive an OTP 30 minutes before the start of your consultation.'
                template_name_for_patient = 'AppointmentBookPatientVirtualBefore30MinByRelative'
                var_list_patient = [doctor_name, appointment_date, appointment_time, slot_duration]

                if is_rescheduled_appointment is True:
                    msg_for_patient = f'We confirm the rescheduling of your video consultation booked by {booked_by_name} with {doctor_name} ({doctor_specialization}) for {int(slot_duration)} minutes on {appointment_date} at {appointment_time}. You will receive an OTP 30 minutes before the start of your consultation.'
                    template_name_for_patient = 'AppointmentReschedulePatientVirtualBefore30MinBySelf'
                    var_list_patient = [doctor_name, slot_duration, appointment_date, appointment_time]

            if appointment_details.appointment_type == "InClinic":
                msg_for_patient = f'Your consultation with {doctor_name} ({doctor_specialization}) is {appointment_status} for {int(slot_duration)} minutes on {appointment_date} at {appointment_time}. Please reach at the clinic 15 minutes before the appointment time.'
                template_name_for_patient = 'AppointmentBookPatientInClinicBefore30MinBySelf'
                var_list_patient = [f'{doctor_name}', slot_duration, f'{appointment_date}, {appointment_time}']

                if is_rescheduled_appointment is True:
                    template_name_for_patient = 'AppointmentReschedulePatientInClinicBefore30MinBySelf'
                    var_list_patient = [doctor_name, slot_duration, appointment_date, appointment_time]

                # if appointment_details.appointment_for != 'Self':
                if appointment_details.appointment_for != 'Self' and patient_id != user_id:
                    msg_for_patient = f'We confirm your consultation booked by {booked_by_name} with {doctor_name} ({doctor_specialization}) for {int(slot_duration)} minutes on {appointment_date} at {appointment_time}. Please reach at the clinic 15 minutes before the appointment time.'
                    template_name_for_patient = 'AppointmentBookPatientInClinicBefore30MinByRelative'
                    var_list_patient = [f'{doctor_name}', slot_duration, f'{appointment_date}, {appointment_time}']

                    if is_rescheduled_appointment is True:
                        msg_for_patient = f'We confirm the rescheduling of your consultation booked by {booked_by_name} with {doctor_name} ({doctor_specialization}) for {int(slot_duration)} minutes on {appointment_date} at {appointment_time}. Please reach at the clinic 15 minutes before the appointment time.'
                        template_name_for_patient = 'AppointmentReschedulePatientInClinicBefore30MinBySelf'
                        var_list_patient = [doctor_name, slot_duration, appointment_date, appointment_time]

                    msg_for_appointment_booking_person = f'The consultation you booked for {patient_name} with {doctor_name} ({doctor_specialization}) is {appointment_status} for {int(slot_duration)} minutes on {appointment_date} at {appointment_time}. {patient_name} should reach at the clinic 15 minutes before the appointment time.'
                    var_list_booked_by_person = [patient_name, doctor_name, f'{appointment_date}, {appointment_time}']

            # appointment_status_doctor = 'rescheduled' if is_rescheduled_appointment is True else 'booked'

            # msg_for_doctor = f'{patient_name} ({patient_gender}, {patient_age} Yrs) has {appointment_status_doctor} an appointment with you for {int(slot_duration)} minutes on {appointment_date} at {appointment_time}'

            if is_rescheduled_appointment is not True:
                template_name_for_doctor = f'AppointmentBookDoctor{str(appointment_details.appointment_type.name)}'
                var_list_doctor = [patient_name, slot_duration, f'{appointment_date}, {appointment_time}']

            else:
                template_name_for_doctor = f'AppointmentRescheduleDoctor{str(appointment_details.appointment_type.name)}'
                var_list_doctor = [patient_name, f'{appointment_date}, {appointment_time}', slot_duration]

            if appointment_details.appointment_slot > date_time_now:

                time_to_start_appointment = appointment_details.appointment_slot - date_time_now

                if time_to_start_appointment < datetime.timedelta(
                        minutes=30) and appointment_details.appointment_type == "Virtual":
                    time_in_minutes = int(time_to_start_appointment.total_seconds() // 60)
                    if jitsi_link:
                        meeting_id = jitsi_link.split('/')[-1]
                        meeting_code = meeting_id[-4:]

                        msg_for_patient = f'Your AYOO Care Video Consultation with {doctor_name} ({doctor_specialization}) will start in {time_in_minutes} minutes. Please join on App or {WEB_URL_PATH} with meeting code- {meeting_code}.'

                        template_name_for_patient = 'AppointmentBookPatientVirtualWithin30MinBySelf'
                        # var_list_patient=[doctor_name, doctor_specialization, time_in_minutes, meeting_code]
                        var_list_patient = [doctor_name, meeting_code]

                elif time_to_start_appointment < datetime.timedelta(
                        minutes=15) and appointment_details.appointment_type == "InClinic":
                    time_in_minutes = int(time_to_start_appointment.total_seconds() // 60)
                    msg_for_patient = f'Your AYOO Care Consultation with {doctor_name} ({doctor_specialization}) will start in {time_in_minutes} minutes. Please be available at the clinic.'
                    template_name_for_patient = 'AppointmentBookPatientInClinicWithin30MinBySelf'
                    var_list_patient = [doctor_name, time_in_minutes]

            aws_mail_ctrl.send_appointment_confirmation(meeting_message=msg_for_patient,
                                                        mobile=patient_details['mobile'],
                                                        email=patient_details['email'])
            # aws_mail_ctrl.send_appointment_confirmation(meeting_message=msg_for_doctor,
            #                                             mobile=doctor_details['mobile'],
            #                                             email=None)

            text_local_controller.send_sms(template_name=template_name_for_patient, var_list=var_list_patient,
                                           numbers=patient_details.get('mobile'))
            text_local_controller.send_sms(template_name=template_name_for_doctor, var_list=var_list_doctor,
                                           numbers=doctor_details.get('mobile'))

            # if appointment_details.appointment_for != 'Self':
            if appointment_details.appointment_for != 'Self' and patient_id != user_id:
                aws_mail_ctrl.send_appointment_confirmation(meeting_message=msg_for_appointment_booking_person,
                                                            mobile=booked_by_mobile,
                                                            email=booked_by_email)
                text_local_controller.send_sms(template_name=template_name_for_booked_by_person,
                                               var_list=var_list_booked_by_person, numbers=booked_by_mobile)

            other_info = AppointmentConfirmationToAdmin(
                appointment_id=appointment_id,
                doctor_name=doctor_name,
                doctor_specialization=doctor_specialization,
                doctor_details=doctor_details,
                patient_name=patient_name,
                appointment_date=appointment_date,
                appointment_time=appointment_time,
                booking_person_name='',
                is_rescheduled_appointment=is_rescheduled_appointment
            )
            self.send_appointment_confirmation_mail_to_admin(appointment_details=appointment_details,
                                                             other_info=other_info)
        except Exception as e:
            logger.info(f'Error occurred while creating notifications of appointment booking')
            logger.info(f'{str(e)}')
            loggers['logger6'].info(f'Error occurred while creating notifications of appointment booking')
            loggers['logger6'].info(f'{str(e)}')
            return None, str(e)

    def manage_first_appointment(self, patient_id):
        try:
            previous_appointment_exist = self.mongo_db['Appointments'].find_one({
                "$and": [
                    {'patient_id': patient_id},
                    {
                        'status.status': {
                            "$in": ['Booked', 'Cancelled', 'Completed', 'No Show']
                        }
                    }
                ]
            })
            is_first_appointment = False
            if previous_appointment_exist is None:
                self.mongo_db['Appointments'].update_many({'patient_id': patient_id}, {
                    "$set": {
                        'is_first_appointment': False
                    }})
                is_first_appointment = True
            return is_first_appointment

        except Exception as e:
            raise Exception(f'Error occurred while managing first appointment tag: {str(e)}')

    def check_for_block_and_available_slots_in_toggle(self, doctor_id: str, appointment_type: str,
                                                      appointment_slot: datetime.datetime, end_time: datetime.datetime):
        try:
            block_slots = list(self.mongo_db['Slots'].find({
                'slot_type': 'BLOCK',
                'doctor_id': doctor_id,
                'sub_type': appointment_type,
                'status': 'active',
                '$or': [
                    {'start_time': {'$lt': end_time}, 'end_time': {'$gt': appointment_slot}},
                    {'start_time': {'$gte': appointment_slot, '$lt': end_time}},
                    {'end_time': {'$gt': appointment_slot, '$lte': end_time}}
                ]
            }).clone())

            if block_slots:
                return False

            weekday = appointment_slot.strftime('%A').lower()

            virtual_slots = self.mongo_db['VirtualSlots'].find_one({'doctorid': doctor_id})
            if not virtual_slots or weekday not in virtual_slots:
                return False

            available_slots = []
            for slot in virtual_slots[weekday]:
                if slot.get('availability_type') == appointment_type:

                    new_time = datetime.datetime.strptime(slot.get('starts_at'), "%I:%M %p").time()
                    starts_on = slot.get("starts_on").replace(hour=new_time.hour, minute=new_time.minute, second=0,
                                                              microsecond=0)
                    new_time = datetime.datetime.strptime(slot.get('ends_at'), "%I:%M %p").time()
                    ends_on = slot.get("ends_on").replace(hour=new_time.hour, minute=new_time.minute, second=0,
                                                          microsecond=0)

                    if (
                            starts_on <= appointment_slot < ends_on and end_time <= ends_on and
                            slot["is_active"]
                    ):
                        #print(slot)
                        available_slots.append(slot)

            if not available_slots:
                return False

            return True
        except Exception as e:
            raise Exception(str(e))

    def check_doctor_slot_availability(self, doctor_id: str, slot_to_book: str, slot_duration: int,
                                       availability_type: str = 'Virtual', clinic_id: str = '', user_id: str = None):
        try:
            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            doctor_data = self.mongo_db['DoctorsInfo'].find_one({
                'doctorid': doctor_id
            })
            if doctor_data['is_active'] is False:
                return None, f'Doctor is not available any more, try booking different slot'

            search_date = str((datetime.datetime.strptime(slot_to_book, "%Y-%m-%d %I:%M %p")).date())

            available_slots, msg = doctor_ctrl.get_virtual_slots_availability(
                request_data=RequestDoctorsVirtualAvailableSlots(specialization='',
                                                                 search_date=search_date,
                                                                 availability_type=availability_type,
                                                                 clinic_id=clinic_id,
                                                                 user_id=user_id), user_id=user_id,
                list_of_doctors=[doctor_data])

            if available_slots is None:
                return None, 'No slots fetched'

            available_slots = [x for x in filter(lambda x: x.get("doctorid") == doctor_id, available_slots)]

            search_time = (datetime.datetime.strptime(slot_to_book, "%Y-%m-%d %I:%M %p")).strftime("%I:%M %p")
            slot_to_search = dict(available_slots[0])['slots'][str(slot_duration)]['available_slots']
            if search_time not in slot_to_search:
                return None, 'Selected slot is no longer available, please try booking with different slot'

            return available_slots, msg

        except Exception as e:
            return None, str(e)

    def book_appointment(self, booking_data: AppointmentBooking, userid: str, booked_by: str = None,
                         booked_by_name: str = 'Self', booked_via: str = "Mobile App", previous_appointments=[]):
        doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
        user_ctrl = UserController(db=self.db, otp_generator=None)
        mh_ctrl = MentalHealthController(db=self.db, mongo=self.mongo)

        open_caseid_exist = mh_ctrl.get_open_case_id(
            patient_ids=[str(userid)],
            doctor_id=str(booking_data.doctorid),
            is_couple_or_family_therapy=False
        )
        appointmentid = str(uuid.uuid4())
        caseid = open_caseid_exist if open_caseid_exist else None
        if (booking_data.payment == "0" or (booking_data.is_confirmed and booking_data.is_active)) and caseid is None:
            caseid = mh_ctrl.generate_case_id()
        patient_name = ''
        patient_details = user_ctrl.get_user_by_id(userid)
        if patient_details:
            patient_name = patient_details.firstname + ' ' + patient_details.lastname

        if booked_by_name == 'Self':
            booked_by_name = patient_name
            booking_data.follow_up_type = 'Paid'

        service_provider = []
        doctor_details, msg = doctor_ctrl.get_by_id(
            doctorid=booking_data.doctorid)
        if doctor_details is not None:
            service_provider = [
                {
                    'doctor_firstname': doctor_details['firstname'],
                    'doctor_lastname': doctor_details['lastname'],
                    'graduation': doctor_details['graduation'],
                    'masters': doctor_details['masters'],
                    'specialization': doctor_details['specialization'],
                    'specialization_field': doctor_details['specialization_field']
                }
            ]
        appointment_slot = datetime.datetime.strptime(
            booking_data.appointment_slot, '%Y-%m-%d %I:%M %p')

        doctor_details, msg = doctor_ctrl.get_by_id(doctorid=booking_data.doctorid)

        if booking_data.slot_duration is None or booking_data.slot_duration == "" or booking_data.slot_duration == 0:

            if doctor_details is not None:
                if booking_data.appointment_type == 'Virtual':
                    end_time = doctor_details['consulting_duration_virtual']
                else:
                    end_time = doctor_details['consulting_duration_clinic']

            else:
                end_time = 30

        else:
            end_time = booking_data.slot_duration
            # 85 minute slot actually books a 90 minute slot
            if end_time == 85:
                end_time = 90

        end_date = appointment_slot + datetime.timedelta(minutes=end_time)
        created_at = booking_data.created_at
        if created_at is None:
            created_at = datetime.datetime.now()
        formatted_date_time = created_at.strftime("%Y-%m-%d %H:%M:%S")
        date_time_now = datetime.datetime.strptime(formatted_date_time, "%Y-%m-%d %H:%M:%S")
        status = {}
        payment_status = "Initiated"
        is_first_appointment = self.manage_first_appointment(patient_id=userid)
        if booking_data.appointment_for is PatientRelationType.Self:

            if booking_data.is_confirmed is None or booking_data.is_confirmed is True:
                payment_status = "Successful"
                status = dict(status='Booked', Reason='', comment='')
            elif booking_data.is_confirmed is False:
                status = dict(status='Initiated', Reason='', comment='')
            appointment_details = AppointmentBookingDetails(

                appointment_id=appointmentid,
                appointment_type=booking_data.appointment_type,
                appointment_for=booking_data.appointment_for,
                symptoms=booking_data.symptoms,
                symptoms_audio_clip=booking_data.symptoms_audio_clip,
                additional_notes=booking_data.additional_notes,
                clinicid=booking_data.clinicid,
                doctorid=booking_data.doctorid,
                doctor_name=doctor_details['firstname'] + ' ' + doctor_details['lastname'],
                appointment_slot=appointment_slot,
                payment=booking_data.payment,
                caseid=caseid,
                patient_id=userid,
                patients=[userid],
                booked_by=booked_by if booked_by else userid,
                created_at=created_at,
                end_date=end_date,
                is_active=booking_data.is_active if booking_data.is_active is not None else True,
                is_confirmed=booking_data.is_confirmed if booking_data.is_confirmed is not None else True,
                status=status,
                booked_by_name=booked_by_name,
                payment_status=payment_status,
                is_first_appointment=is_first_appointment,
                case_open=True,
                follow_up_type=booking_data.follow_up_type,
                care_type=booking_data.care_type,
                booked_via=booked_via,
                previous_appointments=previous_appointments
            )
            if date_time_now > appointment_details.appointment_slot:
                return None, f'Appointment slot {appointment_slot} is not available any more, try booking different slot'

            mongo_collection = self.mongo_db['Appointments']
            try:
                is_appointment_slot_available = self.__available_appointment_slot(
                    appointment_slot=appointment_slot, doctor_id=booking_data.doctorid)

                if not is_appointment_slot_available:
                    return None, f'Appointment slot {appointment_slot} is not available any more, try booking different slot'

                is_doctor_available, msg = self.check_doctor_slot_availability(doctor_id=booking_data.doctorid,
                                                                               slot_to_book=booking_data.appointment_slot,
                                                                               slot_duration=booking_data.slot_duration,
                                                                               availability_type=booking_data.appointment_type,
                                                                               clinic_id=booking_data.clinicid,
                                                                               user_id=appointment_details.patient_id)
                if not is_doctor_available:
                    return None, msg

                old_appointment = mongo_collection.find_one(
                    {"doctorid": booking_data.doctorid, "patient_id": userid, "appointment_slot": appointment_slot})
                if old_appointment is not None:
                    appointment_details.appointment_id = old_appointment.get("appointment_id")
                    appointmentid = old_appointment.get("appointment_id")
                mongo_collection.delete_many(
                    {"doctorid": booking_data.doctorid, "patient_id": userid, "appointment_slot": appointment_slot})
                mongo_collection.insert_one(dict(appointment_details))

                if booking_data.appointment_type is AppointmentType.Virtual:
                    jitsi_ctrl = JitsiMeetController(db=self.db, mongo=self.mongo)
                    jitsi_ctrl.create_meeting_link(
                        appointment_id=appointmentid, case_id=caseid,
                        doctorid=str(booking_data.doctorid),
                        patient_ids=[str(userid)],
                        appointment_slot=appointment_slot
                    )
                _ = CaseSheetDAO().clear_follow_ups(caseid)


            except Exception as e:
                err = str(e)
                return None, f'Internal Error code {err} for booking appointment'
            response_data = dict(
                appointment_id=appointmentid,
                patient_name=patient_name,
                caseid=caseid,
                appointment_slot=str(appointment_details.appointment_slot),
                reason_for_visit=booking_data.symptoms,
                service_provider=service_provider,
                payment=booking_data.payment,
                end_time=str(end_date)
            )

            if doctor_details is not None and appointment_details.is_active is True and appointment_details.is_confirmed is True:
                self.manage_notifications_for_booked_appointments(appointment_id=appointmentid,
                                                                  is_rescheduled_appointment=booking_data.is_rescheduled_appointment)
            self.change_new_user_to_old_user(user_id=appointment_details.patient_id)
            self.change_new_user_to_old_user(user_id=appointment_details.booked_by)

            return response_data, 'Appointment Booked'

        else:
            return None, 'Appointment could not be booked'

    def add_multiple_relations(self, booking_data: AppointmentBookingForOthers, logged_in_user: str, booked_by_id: str = None,
                                booked_by_name: str = 'Self',):
        try:
            pt_ctrl = RelativesController(db=self.db, mongo=self.mongo)

            all_patients_ids=[]
            all_patients=[]
            care_taker_details = self.__get_patient_details(patientid=logged_in_user)

            if booking_data.appointment_for in ['Couple', 'Family']:
                all_patients_ids.append(logged_in_user)
                all_patients.append(dict(
                        patient_id=logged_in_user,
                        patient_name=care_taker_details.get('firstname')+' '+care_taker_details.get('lastname'),
                        patient_dob=care_taker_details.get('dob'),
                        patient_gender=care_taker_details.get('gender')
                    ))

            other_relation_allowed = True

            for _patient in booking_data.patients:
                patient_exist_in_user_db = pt_ctrl.get_user_by_details(user_data=CreateUserByAdmin(
                    firstname=_patient.firstname,
                    lastname=_patient.lastname,
                    dob=_patient.dob,
                    email=_patient.email,
                    mobile=_patient.mobile,
                    gender=_patient.gender
                ))


                if patient_exist_in_user_db:
                    patient_name = patient_exist_in_user_db.firstname + ' ' + patient_exist_in_user_db.lastname
                    patient_id = patient_exist_in_user_db.userid
                    patient_dob = patient_exist_in_user_db.birthdate
                    patient_gender = patient_exist_in_user_db.gender

                    all_patients_ids.append(patient_id)
                    all_patients.append(dict(
                        patient_id=patient_id,
                        patient_name=patient_name,
                        patient_dob=patient_dob,
                        patient_gender=patient_gender
                    ))

                    user_details = self.__get_patient_details(patientid=patient_id)
                    add_both_relatives = pt_ctrl.create_relations_and_relationships(
                        caretaker=logged_in_user,
                        patient=patient_id,
                        relation_of_patient_with_caretaker=_patient.relation,
                        caretaker_details=care_taker_details,
                        patient_details=user_details,
                        caretaker_registered=True,
                        patient_registered=True,
                        active_flag=True,
                        other_relation_allowed=other_relation_allowed
                    )

                else:
                    get_patient_details = pt_ctrl.get_relative_by_details(
                        caretaker_id=logged_in_user,
                        relative_data=AddFamilyMember(
                            firstname=_patient.firstname,
                            lastname=_patient.lastname,
                            dob=_patient.dob,
                            email=_patient.email,
                            mobile=_patient.mobile,
                            gender=_patient.gender,
                            relation=_patient.relation
                        )
                    )
                    if get_patient_details:
                        patient_name = get_patient_details[0].firstname + \
                                       ' ' + get_patient_details[0].lastname
                        patient_id = get_patient_details[0].relativeid
                        patient_dob = get_patient_details[0].birthdate
                        patient_gender = (get_patient_details[0].gender)[0]

                        all_patients_ids.append(patient_id)
                        all_patients.append(dict(
                            patient_id=patient_id,
                            patient_name=patient_name,
                            patient_dob=patient_dob,
                            patient_gender=patient_gender
                        ))
                    else:
                        patient_age = get_age_in_years(str(_patient.dob))
                        if patient_age < 18 and _patient.relation in ['Others', 'Family']:
                            raise Exception('Other/Family/Couple relation type is not allowed for minors')
                        if patient_age >= 18:
                            email_exists = self.db.query(DBUser).filter(DBUser.email == _patient.email).one_or_none()
                            if email_exists is not None:
                                return None, f"Email {_patient.email} already exists"

                            mobile_exists = self.db.query(DBUser).filter(DBUser.mobile == _patient.mobile).one_or_none()
                            if mobile_exists is not None:
                                return None, f"Mobile {_patient.mobile} already exists"

                            eighteen_years_ago = datetime.datetime.now() - relativedelta(years=18)

                            email_exists = self.db.query(DBRelatives).filter(
                                    DBRelatives.email == _patient.email, DBRelatives.birthdate <= eighteen_years_ago).all()
                            if len(email_exists) > 0:
                                return None, f"A user with email {_patient.email} already exists in another AYOO family account"

                            mobile_exists = self.db.query(DBRelatives).filter(
                                DBRelatives.mobile == _patient.mobile, DBRelatives.birthdate <= eighteen_years_ago).all()
                            if len(mobile_exists) >0:
                                return None, f"A user with mobile {_patient.mobile} already exists in another AYOO family account"

                        relation_of_caretaker_with_patient = None
                        if _patient.relation == 'Parent':
                            relation_of_caretaker_with_patient = 'Child'

                        if _patient.relation == 'Spouse':
                            relation_of_caretaker_with_patient = 'Spouse'

                        if _patient.relation == 'Child':
                            relation_of_caretaker_with_patient = 'Parent'

                        if _patient.relation == 'Family':
                            relation_of_caretaker_with_patient = 'Family'

                        if other_relation_allowed is True and _patient.relation == 'Others':
                            relation_of_caretaker_with_patient = 'Others'

                        if relation_of_caretaker_with_patient is None:
                            return None, 'Cannot define relations'

                        patient_details, msg = pt_ctrl.create_relative(
                            caretaker_id=logged_in_user,
                            relative_data=AddFamilyMember(
                                firstname=_patient.firstname,
                                lastname=_patient.lastname,
                                dob=_patient.dob,
                                email=_patient.email,
                                mobile=_patient.mobile,
                                gender=_patient.gender,
                                relation=_patient.relation
                            ),
                            caretaker_data=dict(
                                user_id=logged_in_user,
                                ayoo_id=care_taker_details.get('ayoo_id',''),
                                firstname=care_taker_details['firstname'],
                                lastname=care_taker_details['lastname'],
                                dob=care_taker_details['dob'],
                                email=care_taker_details['email'],
                                mobile=care_taker_details['mobile'],
                                gender=care_taker_details['gender'],
                                relation=relation_of_caretaker_with_patient
                            ),
                            active_flag=True
                        )
                        if patient_details:
                            relative_one = patient_details['relative_1'].to_dict()
                            relative_two = patient_details['relative_2'].to_dict()

                            patient_name = relative_one['firstname'] + \
                                           ' ' + relative_one['lastname']
                            patient_id = relative_one['relativeid']
                            patient_dob = relative_one['birthdate']
                            patient_gender = relative_one['gender']
                            all_patients_ids.append(patient_id)
                            all_patients.append(dict(
                                patient_id=patient_id,
                                patient_name=patient_name,
                                patient_dob=patient_dob,
                                patient_gender=patient_gender
                            ))
                        else:
                            return None, f'Could not record patient\'s data: {msg}'


            return dict(
                care_taker_id=logged_in_user,
                care_taker_details = all_patients[0],
                all_patient_ids = all_patients_ids,
                all_patients = all_patients
            ), 'Relations added'

        except Exception as e:
            return None, str(e)


    def book_appointment_others(self, booking_data: AppointmentBookingForOthers, logged_in_user: str, booked_by_id: str = None,
                                booked_by_name: str = 'Self', booked_via = "Mobile App", previous_appointments = []):

        if booking_data.appointment_for == PatientRelationType.Self:
            return None, f'Appointment could not be booked'

        appointmentid = str(uuid.uuid4())

        doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)

        all_patients, err_msg = self.add_multiple_relations(booking_data=booking_data, logged_in_user=logged_in_user)
        if all_patients is None:
            return None, err_msg

        all_patient_ids = all_patients.get('all_patient_ids', [])

        care_taker_details = all_patients.get('care_taker_details',{})

        if booked_by_name == 'Self':
            booked_by_name = care_taker_details.get('patient_name')
            booking_data.follow_up_type = 'Paid'

        is_couple_or_family_therapy = True if booking_data.appointment_for in ['Couple', 'Family'] else False

        mh_ctrl = MentalHealthController(db=self.db, mongo=self.mongo)
        open_caseid_exist = mh_ctrl.get_open_case_id(
            patient_ids=all_patients.get('all_patient_ids', []),
            doctor_id=str(booking_data.doctorid),
            is_couple_or_family_therapy=is_couple_or_family_therapy
            )
        case_id = open_caseid_exist if open_caseid_exist else None

        if (booking_data.payment == "0" or (booking_data.is_confirmed and booking_data.is_active)) and case_id is None:
            case_id = mh_ctrl.generate_case_id()

        service_provider = []
        doctor_details, msg = doctor_ctrl.get_by_id(
            doctorid=booking_data.doctorid
        )
        if doctor_details is None:
            return None, f'Doctor details not found for doctor id:{booking_data.doctorid}'

        service_provider = [
            {
                'doctor_firstname': doctor_details['firstname'],
                'doctor_lastname': doctor_details['lastname'],
                'graduation': doctor_details['graduation'],
                'masters': doctor_details['masters'],
                'specialization': doctor_details['specialization'],
                'specialization_field': doctor_details['specialization_field']
            }
        ]

        appointment_slot = datetime.datetime.strptime(
            booking_data.appointment_slot, '%Y-%m-%d %I:%M %p')

        if booking_data.slot_duration is None or booking_data.slot_duration == "" or booking_data.slot_duration == 0:

            if booking_data.appointment_type == 'Virtual':
                end_time = doctor_details['consulting_duration_virtual']
            else:
                end_time = doctor_details['consulting_duration_clinic']

        else:
            end_time = booking_data.slot_duration
            # 85 minute actually books a 90 minute slot
            if end_time == 85:
                end_time = 90

        end_date = appointment_slot + datetime.timedelta(minutes=end_time)
        created_at = booking_data.created_at
        if created_at is None:
            created_at = datetime.datetime.now()
        formatted_date_time = created_at.strftime("%Y-%m-%d %H:%M:%S")
        date_time_now = datetime.datetime.strptime(formatted_date_time, "%Y-%m-%d %H:%M:%S")
        status = {}
        payment_status = "Initiated"
        if booking_data.is_confirmed is None or booking_data.is_confirmed is True:
            payment_status = "Successful"
            status = dict(status='Booked', Reason='', comment='')
        elif booking_data.is_confirmed is False:
            status = dict(status='Initiated', Reason='', comment='')


        is_first_appointment = False
        for _patient in all_patient_ids:
            is_first_appointment = self.manage_first_appointment(patient_id=_patient)

        appointment_details = AppointmentBookingDetails(

            appointment_id=appointmentid,
            appointment_type=booking_data.appointment_type,
            appointment_for=booking_data.appointment_for,
            symptoms=booking_data.symptoms,
            symptoms_audio_clip=booking_data.symptoms_audio_clip,
            additional_notes=booking_data.additional_notes,
            clinicid=booking_data.clinicid,
            doctorid=booking_data.doctorid,
            doctor_name=doctor_details['firstname'] + ' ' + doctor_details['lastname'],
            appointment_slot=appointment_slot,
            payment=booking_data.payment,
            caseid=case_id,
            patient_id=all_patient_ids[0],
            patients=all_patient_ids,
            booked_by=logged_in_user if booked_by_id is None else booked_by_id,
            created_at=created_at,
            end_date=end_date,
            is_active=booking_data.is_active if booking_data.is_active is not None else True,
            is_confirmed=booking_data.is_confirmed if booking_data.is_confirmed is not None else True,
            status=status,
            booked_by_name=booked_by_name,
            payment_status=payment_status,
            is_first_appointment=is_first_appointment,
            case_open=True,
            follow_up_type=booking_data.follow_up_type,
            care_type=booking_data.care_type,
            booked_via = booked_via,
            previous_appointments = previous_appointments,
            is_couple_or_family_therapy = is_couple_or_family_therapy
        )
        if date_time_now > appointment_details.appointment_slot:
            return None, f'Appointment slot {appointment_slot} is not available any more, try booking different slot'

        mongo_collection = self.mongo_db['Appointments']

        try:
            is_appointment_slot_available = self.__available_appointment_slot(
                appointment_slot=appointment_slot, doctor_id=booking_data.doctorid)

            if not is_appointment_slot_available:
                return None, f'Appointment slot {appointment_slot} is not available any more, try booking different slot'

            is_doctor_available, msg = self.check_doctor_slot_availability(
                doctor_id=booking_data.doctorid,
                slot_to_book=booking_data.appointment_slot,
                slot_duration=booking_data.slot_duration,
                availability_type=booking_data.appointment_type,
                clinic_id=booking_data.clinicid,
                user_id=appointment_details.patient_id
            )

            if not is_doctor_available:
                return None, msg


            old_appointment = mongo_collection.find_one({
                "doctorid": booking_data.doctorid,
                "patient_id": all_patient_ids[0],
                "patients": {
                    '$in': all_patient_ids
                },
                "appointment_slot": appointment_slot
            })
            if old_appointment is not None:
                appointment_details.appointment_id = old_appointment.get("appointment_id")
                appointmentid = old_appointment.get("appointment_id")

            mongo_collection.delete_many({"patient_id": all_patient_ids[0], "doctorid": booking_data.doctorid, "appointment_slot": appointment_slot})

            mongo_collection.insert_one(dict(appointment_details))

            if booking_data.appointment_type is AppointmentType.Virtual:
                jitsi_ctrl = JitsiMeetController(db=self.db, mongo=self.mongo)
                jitsi_ctrl.create_meeting_link(
                    appointment_id=appointmentid, case_id=case_id,
                    doctorid=str(booking_data.doctorid),
                    patient_ids=all_patient_ids,
                    appointment_slot=appointment_slot
                )

            _ = CaseSheetDAO().clear_follow_ups(case_id)


        except Exception as e:
            err = str(e)
            return None, '', f'Internal Error code {err} for booking appointment'

        response_data = dict(
            appointment_id=appointmentid,
            # patient_name=patient_name,
            caseid=case_id,
            appointment_slot=str(appointment_details.appointment_slot),
            reason_for_visit=booking_data.symptoms,
            service_provider=service_provider,
            payment=booking_data.payment,
            end_time=str(end_date)
        )
        if doctor_details and appointment_details.is_active is True and appointment_details.is_confirmed is True:
            self.manage_notifications_for_booked_appointments(appointment_id=appointmentid,
                                                              is_rescheduled_appointment=booking_data.is_rescheduled_appointment)

        # Change new user to old user
        self.change_new_user_to_old_user(user_id=appointment_details.patient_id)

        return response_data, 'Appointment Booked'

    def book_appointment_guest(self, booking_data: GuestAppointmentBooking, booked_via="Mobile App"):

        if booking_data.appointment_for != PatientRelationType.Self:
            return None, f'Appointment could not be booked'

        user_ctrl = UserController(db=self.db, otp_generator=None)
        patient_details = user_ctrl.get_user_by_email_mobile(login_or_email=booking_data.email)
        if patient_details is None:
            patient_details = user_ctrl.get_user_by_email_mobile(login_or_email=booking_data.mobile)

        if patient_details is None:
            return None, 'No user exist with given email or mobile. Please register first.'

        resp, msg = self.book_appointment(booking_data=AppointmentBooking(
            appointment_type=booking_data.appointment_type,
            appointment_for=booking_data.appointment_for,
            symptoms=booking_data.symptoms,
            symptoms_audio_clip=booking_data.symptoms_audio_clip,
            additional_notes=booking_data.additional_notes,
            clinicid=booking_data.clinicid,
            doctorid=booking_data.doctorid,
            appointment_slot=booking_data.appointment_slot,
            is_active=booking_data.is_active,
            is_confirmed=booking_data.is_confirmed,
            payment=booking_data.payment,
            slot_duration=booking_data.slot_duration,
            follow_up_type=booking_data.follow_up_type,
            care_type=booking_data.care_type
        ), userid=patient_details.userid, booked_via=booked_via)

        return resp, msg

    def book_appointment_guest_for_others(self, booking_data: GuestAppointmentBookingForOthers,
                                          booked_via="Mobile App"):

        if booking_data.appointment_for == PatientRelationType.Self:
            return None, f'Appointment could not be booked'

        user_ctrl = UserController(db=self.db, otp_generator=None)
        patient_details = user_ctrl.get_user_by_email_mobile(login_or_email=booking_data.email)
        if patient_details is None:
            patient_details = user_ctrl.get_user_by_email_mobile(login_or_email=booking_data.mobile)

        if patient_details is None:
            return None, 'No user exist with given email or mobile. Please register first.'

        resp, msg = self.book_appointment_others(booking_data=AppointmentBookingForOthers(
            appointment_type=booking_data.appointment_type,
            appointment_for=booking_data.appointment_for,
            symptoms=booking_data.symptoms,
            symptoms_audio_clip=booking_data.symptoms_audio_clip,
            additional_notes=booking_data.additional_notes,
            clinicid=booking_data.clinicid,
            doctorid=booking_data.doctorid,
            appointment_slot=booking_data.appointment_slot,
            firstname=booking_data.patient_firstname,
            lastname=booking_data.patient_lastname,
            dob=booking_data.patient_dob,
            email=booking_data.patient_email,
            mobile=booking_data.patient_mobile,
            gender=booking_data.patient_gender,
            is_active=booking_data.is_active,
            is_confirmed=booking_data.is_confirmed,
            payment=booking_data.payment,
            slot_duration=booking_data.slot_duration,
            follow_up_type=booking_data.follow_up_type,
            care_type=booking_data.care_type
        ), logged_in_user=patient_details.userid, booked_via = booked_via)

        return resp, msg

    def add_appointment_by_admin(self, booking_data: AppointmentBookingByAdmin, admin_id: str):
        try:
            user_ctrl = UserController(db=self.db, otp_generator=None)
            rel_ctrl = RelativesController(db=self.db, mongo=self.mongo)
            admin_details = self.mongo_db['AdminCollection'].find_one(dict(admin_id=admin_id))

            if admin_details is None:
                return None, 'Invalid Admin ID'

            admin_name = admin_details['admin_name'] if 'admin_name' in admin_details else 'Admin'

            booked_by = booking_data.booked_by
            if booked_by == '':
                booked_by = admin_id
            booked_via = "Admin"
            fees = booking_data.payment
            booked_on = datetime.datetime.now()
            booker_details = user_ctrl.get_user_by_id(userid=booked_by)
            if booker_details is None:
                booker_details = rel_ctrl.get_relative_by_id(relativeid=booked_by)
                setattr(booker_details, "userid", booker_details.relativeid)
            previous_appointments = []

            if booking_data.is_rescheduled_appointment is True and booking_data.appointment_cancellation_details is not None:
                try:
                    appointment = AppointmentDAO().list(0, 1, CaseSheetQueryFields(
                        appointment_id=booking_data.appointment_cancellation_details.appointment_id))[0]
                except Exception as e:
                    return None, f"Appointment {booking_data.appointment_cancellation_details} not found!!!!!"
                previous_appointments.extend(appointment.previous_appointments)
                previous_appointments.append({**booking_data.appointment_cancellation_details.dict(),
                                              "appointment_slot": appointment.appointment_slot,
                                              "booked_by": appointment.booked_by,
                                              "appointment_type": appointment.appointment_type, "updated_by": booked_by,
                                              "booked_by_name": appointment.booked_by_name,
                                              "updated_by_name": admin_name,
                                              "rescheduled_date": datetime.datetime.now()})
                booked_via = appointment.booked_via
                fees = appointment.payment
                booked_on = appointment.created_at
                appointment.payment = 0
                try:
                    id = appointment.id
                    delattr(appointment, "id")
                    resp = AppointmentDAO().update(_id=id, _obj=appointment)
                except Exception as e:
                    print(str(e))

            if booking_data.appointment_for == 'Self':

                patient_details = user_ctrl.get_user_by_id(userid=booking_data.patientid)
                if patient_details is None:
                    patient_details = rel_ctrl.get_relative_by_id(relativeid=booking_data.patientid)
                    setattr(patient_details, "userid", patient_details.relativeid)
                if patient_details is None:
                    return None, 'Invalid patient ID'

                resp, msg = self.book_appointment(
                    booking_data=AppointmentBooking(
                        appointment_type=booking_data.appointment_type,
                        appointment_for=booking_data.appointment_for,
                        symptoms=booking_data.symptoms,
                        symptoms_audio_clip=booking_data.symptoms_audio_clip,
                        additional_notes=booking_data.additional_notes,
                        clinicid=booking_data.clinicid,
                        doctorid=booking_data.doctorid,
                        appointment_slot=booking_data.appointment_slot,
                        is_active=booking_data.is_active,
                        is_confirmed=booking_data.is_confirmed,
                        payment=fees,
                        slot_duration=booking_data.slot_duration,
                        follow_up_type=booking_data.follow_up_type,
                        care_type=booking_data.care_type,
                        created_at=booked_on,
                        is_rescheduled_appointment=booking_data.is_rescheduled_appointment
                    ), userid=patient_details.userid, booked_by=booked_by,
                    booked_by_name=patient_details.firstname + " " + patient_details.lastname, booked_via=booked_via,
                    previous_appointments=previous_appointments)
            else:
                if not booking_data.patients:
                    return None, 'Guest details are required - First Name, Last Name, Email, Mobile, Gender, Date of Birth'

                user_ctrl = UserController(db=self.db, otp_generator=None)
                booking_person_details = user_ctrl.get_user_by_id(userid=booking_data.patientid)
                if booking_person_details is None:
                    return None, 'Invalid patient ID'

                resp, msg = self.book_appointment_others(booking_data=AppointmentBookingForOthers(
                    appointment_type=booking_data.appointment_type,
                    appointment_for=booking_data.appointment_for,
                    symptoms=booking_data.symptoms,
                    symptoms_audio_clip=booking_data.symptoms_audio_clip,
                    additional_notes=booking_data.additional_notes,
                    clinicid=booking_data.clinicid,
                    doctorid=booking_data.doctorid,
                    appointment_slot=booking_data.appointment_slot,
                    patients=booking_data.patients,
                    # firstname=booking_data.firstname,
                    # lastname=booking_data.lastname,
                    # dob=booking_data.dob,
                    # email=booking_data.email,
                    # mobile=booking_data.mobile,
                    # gender=booking_data.gender,
                    is_active=booking_data.is_active,
                    is_confirmed=booking_data.is_confirmed,
                    payment=fees,
                    created_at=booked_on,
                    slot_duration=booking_data.slot_duration,
                    follow_up_type=booking_data.follow_up_type,
                    care_type=booking_data.care_type,
                    is_rescheduled_appointment=booking_data.is_rescheduled_appointment
                ), logged_in_user=booking_data.patientid, booked_by_id=booked_by, booked_by_name=booking_person_details.firstname + " " + booking_person_details.lastname, booked_via = booked_via, previous_appointments = previous_appointments)

            if booking_data.is_rescheduled_appointment is True and booking_data.appointment_cancellation_details is not None:
                _r = self.update_appointment_status(app_view=booking_data.appointment_cancellation_details,
                                                    next_appointment_details=resp)

            return resp, msg
        except Exception as e:
            return None, str(e)

    def remove_appointment_by_admin(self, appointment_id: str):
        mongo_collection_appointments = self.mongo_db['Appointments']
        mongo_collection_appointments.find_one_and_delete({"appointment_id": appointment_id})

    def get_members_appointments(self, userid: str, switched_account_status: bool, caretaker_id: str,
                                 request_data: RequestAppointmentList, consent: bool = False):
        try:
            appointments = []
            docs_new = []
            if request_data.starts_from:
                date_from = datetime.datetime.strptime(
                    request_data.starts_from, '%Y-%m-%d')
            else:
                date_from = None

            if request_data.till:
                date_till = datetime.datetime.strptime(
                    request_data.till, '%Y-%m-%d')
            else:
                # date_till = date_from + datetime.timedelta(hours=+22, minutes=+59, seconds=+59)
                date_till = date_from + datetime.timedelta(days=365 * 2)

            mongo_collection_appointments = self.mongo_db['Appointments']
            if switched_account_status == False:
                from .DAOs.relativeDAO import RelativeDAO

                all_relatives = RelativeDAO().get_all_relatives(user_id=userid)
                all_relatives = filter(lambda x: check_minor(x.birthdate.strftime("%Y-%m-%d")),
                                       all_relatives)  # to be changed
                appointments_list = list(mongo_collection_appointments.find(
                    {
                        '$or': [
                            {'patient_id': userid},
                            {'booked_by': userid, 'patient_id': {'$nin': [x.relativeid for x in all_relatives]}}
                        ],
                        '$and': [
                            {'appointment_slot': {'$gte': date_from, '$lte': date_till}},
                            {'is_active': True},
                            {'is_confirmed': True},
                            {"extension": {"$ne": True}}

                        ]
                    }
                )
                )
            if switched_account_status == True and not consent:
                appointments_list = list(mongo_collection_appointments.find(
                    {
                        '$and': [
                            {
                                '$or': [
                                    {'patient_id': userid,
                                     'booked_by': caretaker_id}
                                ]
                            },
                            {'appointment_slot': {
                                '$gte': date_from, '$lte': date_till}},
                            {'is_active': True},
                            {'is_confirmed': True}
                        ]
                    }
                )
                )
            if switched_account_status == True and consent:
                appointments_list = list(mongo_collection_appointments.find(
                    {
                        'patient_id': userid,
                        'appointment_slot': {
                            '$gte': date_from, '$lte': date_till},
                        'is_active': True,
                        'is_confirmed': True
                    }
                )
                )
            if appointments_list:
                doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
                for docs in appointments_list:
                    status = ''
                    if 'status' in docs:
                        status = docs['status']['status'] if 'status' in docs['status'] else ''
                    
                    # Handle multiple patients if patients array exists
                    patient = None
                    patient_name = ""
                    if 'patients' in docs and docs['patients'] and len(docs['patients']) > 1:
                        # Multiple patients case
                        patients_array = []
                        patient_names = []
                        for patient_id in docs['patients']:
                            patient_details = self.__get_patient_details(patientid=patient_id)
                            if patient_details:
                                patients_array.append(patient_details)
                                patient_names.append(f"{patient_details.get('firstname', '')} {patient_details.get('lastname', '')}")
                        patient = patients_array
                        patient_name = ", ".join(patient_names)
                    else:
                        # Single patient case (original logic)
                        patient = [self.__get_patient_details(patientid=docs['patient_id'])]
                        if patient:
                            patient_name = f"{patient[0].get('firstname', '')} {patient[0].get('lastname', '')}"
                    
                    clinic = None
                    doctor_details = None
                    get_doctor_details, msg = doctor_ctrl.get_by_id(
                        doctorid=docs['doctorid'])
                    if get_doctor_details is not None:
                        doctor_details = {
                            'name': get_doctor_details['firstname'] + ' ' + get_doctor_details['lastname'],
                            'languages': get_doctor_details['languages'],
                            'graduation': get_doctor_details['graduation'],
                            'masters': get_doctor_details['masters'],
                            'doctortype': get_doctor_details['doctortype'],
                            'specialization': get_doctor_details['specialization'],
                            'specialization_field': get_doctor_details['specialization_field'],
                            'additional_qualification': get_doctor_details['additional_qualification'],
                            'bio': get_doctor_details['bio'],
                            'consulting_duration_virtual': get_doctor_details['consulting_duration_virtual'],
                            'consulting_duration_clinic': get_doctor_details['consulting_duration_clinic'],
                            'consulting_fees_virtual': get_doctor_details['consulting_fees_virtual'],
                            'consulting_fees_clinic': get_doctor_details['consulting_fees_clinic'],
                            'image_id': get_doctor_details['image_id'],
                            'profile_image_url': get_doctor_details['profile_image_url']
                        }

                    if docs['clinicid']:
                        clinic_details = doctor_ctrl.get_clinic_by_id(
                            clinicid=docs['clinicid'])
                        if clinic_details:
                            clinic = dict(
                                clinic_id=clinic_details.clinicid,
                                clinic_name=clinic_details.name,
                                clince_mobile=clinic_details.mobile,
                                clinic_starts_at=clinic_details.starts_at,
                                clinic_ends_at=clinic_details.ends_at,
                                clinic_address=clinic_details.address,
                                lat=clinic_details.lat,
                                lon=clinic_details.lon
                            )
                    docs_new.append(docs)

                    # Appending jitsi meeting link info in the response
                    meeting_link_info = None
                    meeting_code = None
                    if docs['appointment_type'] == 'Virtual':
                        jitsi_ctrl = JitsiMeetController(db=self.db, mongo=self.mongo)
                        meeting_info = jitsi_ctrl.check_meeting_using_appointment_id(
                            appointment_id=str(docs['appointment_id']))

                        meeting_link_info = meeting_info[
                            'patient_joining_link'] if meeting_info is not None and 'patient_joining_link' in meeting_info else None
                        meeting_code = meeting_info[
                            'meeting_code'] if meeting_info is not None and 'meeting_code' in meeting_info else None

                    slot_duration = int((docs['end_date'] - docs['appointment_slot']).total_seconds() / 60)
                    appointments.append(
                        dict(ResponseMemberAppointmentListCopy(
                            appointment_id=docs['appointment_id'],
                            caseid=docs['caseid'],
                            symptoms=docs['symptoms'],
                            symptoms_audio_clip=docs['symptoms_audio_clip'] if
                            'symptoms_audio_clip' in docs else 'None',
                            additional_notes=docs['additional_notes'] if 'additional_notes' in docs else 'None',
                            appointment_slot=docs['appointment_slot'],
                            slot_duration=slot_duration,
                            end_date=docs['end_date'],
                            appointment_type=docs['appointment_type'],
                            appointment_for=docs['appointment_for'],
                            patient=patient,
                            patient_name=patient_name,
                            clinic=clinic,
                            doctor=doctor_details,
                            meeting_link=meeting_link_info,
                            meeting_code=meeting_code,
                            appointment_status=status,
                            booking_cost=docs['payment']
                        ))
                    )
            # for app in appointments:
            #     loggers['logger1'].info("/user/my/appointments : response : " + str(dict(app)))
            if len(appointments):
                resp = sorted(appointments, key=lambda i: i['appointment_slot'])
                return resp, 'appointments found'
            else:
                return None, 'No appointments'
        except Exception as e:
            from traceback import print_exc
            print_exc()
            raise HTTPException(status_code=401, detail=str(e))

    def get_guest_appointments(self, request_data: RequestAppointmentList):

        guest_ctrl = GuestController(db=self.db, mongo=self.mongo)
        guest_details = guest_ctrl.get_guest_by_details(
            guest_date=request_data)
        userid = None

        if guest_details:
            userid = guest_details[0].guestid
        else:
            return None, 'Guest with requested details, not found.'

        appointments = []
        if request_data.starts_from:
            date_from = datetime.datetime.strptime(
                request_data.starts_from, '%Y-%m-%d')
        else:
            date_from = None

        if request_data.till:
            date_till = datetime.datetime.strptime(
                request_data.till, '%Y-%m-%d')
        else:
            date_till = date_from + \
                        datetime.timedelta(hours=+22, minutes=+59, seconds=+59)

        mongo_collection_appointments = self.mongo_db['Appointments']
        appointments_list = list(mongo_collection_appointments.find(
            {
                '$or': [
                    {'patient_id': userid},
                    {'booked_by': userid}
                ],
                '$and': [
                    {'appointment_slot': {'$gte': date_from, '$lte': date_till}},
                    {'is_active': True},
                    {'is_confirmed': True}
                ]
            }
        )
        )
        if appointments_list:
            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            for docs in appointments_list:
                patient = self.__get_patient_details(
                    patientid=docs['patient_id'])
                clinic = None
                doctor_details = None
                get_doctor_details, msg = doctor_ctrl.get_by_id(
                    doctorid=docs['doctorid'])
                logger.info(f'getdoctor: {get_doctor_details}')
                if get_doctor_details is not None:
                    doctor_details = {
                        'name': get_doctor_details['firstname'] + ' ' + get_doctor_details['lastname'],
                        'languages': get_doctor_details['languages'],
                        'graduation': get_doctor_details['graduation'],
                        'masters': get_doctor_details['masters'],
                        'doctortype': get_doctor_details['doctortype'],
                        'specialization': get_doctor_details['specialization'],
                        'specialization_field': get_doctor_details['specialization_field'],
                        'additional_qualification': get_doctor_details['additional_qualification'],
                        'bio': get_doctor_details['bio']
                    }
                if docs['clinicid']:
                    clinic_details = doctor_ctrl.get_clinic_by_id(
                        clinicid=docs['clinicid'])
                    if clinic_details:
                        clinic = clinic_details.name
                appointments.append(
                    ResponseMemberAppointmentList(
                        appointment_id=docs['appointment_id'],
                        caseid=docs['caseid'],
                        symptoms=docs['symptoms'],
                        symptoms_audio_clip=docs['symptoms_audio_clip'],
                        additional_notes=docs['additional_notes'],
                        appointment_slot=docs['appointment_slot'],
                        appointment_type=docs['appointment_type'],
                        appointment_for=docs['appointment_for'],
                        patient=patient,
                        clinic=clinic,
                        doctor=doctor_details
                    )
                )
        if len(appointments):
            resp = appointments
            return resp, ''
        else:
            return None, 'No appointments'

    def available_slot(self, doctorid: str, start_time, end_time, duration, search_date, slot_id):
        starts_at = start_time
        ends_at = end_time
        date_from = datetime.datetime.strptime(search_date, '%Y-%m-%d')
        date_till = date_from + datetime.timedelta(hours=+22, minutes=+59, seconds=+59)
        time_format = '%I:%M %p'
        available_from = datetime.datetime.strptime(starts_at, time_format)
        available_till = datetime.datetime.strptime(ends_at, time_format)
        slot = available_from
        available_slots = []

        # Handle 85-minute consultation time to actually book a 90-minute slot
        actual_duration = duration
        if duration == 85:
            actual_duration = 90

        while slot < available_till:
            available_slots.append(slot.strftime(time_format))
            slot = slot + datetime.timedelta(hours=0, minutes=int(actual_duration))

        # check appointments for same date
        appointments_list = []
        mongo_collection_appointments = self.mongo_db['Appointments']
        appointments_list = list(mongo_collection_appointments.find(
            {
                '$and': [{'doctorid': doctorid},
                         {'appointment_slot': {'$gte': date_from, '$lte': date_till}},
                         {'appointment_type': 'InClinic'},
                         {'is_active': True},
                         {'is_confirmed': True}
                         ]
            }
        )
        )

        # remove already booked slot
        if appointments_list:
            for appointments in appointments_list:
                appointment_time = datetime.datetime.strftime(
                    appointments['appointment_slot'], time_format)
                if appointment_time in available_slots:
                    available_slots.remove(
                        appointment_time)

        # Removed Blocked Slots

        if available_slots:
            available_slots_copy = copy.deepcopy(available_slots)
            # slot_id = None
            slot_db_res: DBBlockSlots
            slot_db_res = self.db.query(DBBlockSlots).filter_by(doctor_id=str(doctorid)).filter_by(
                slot_id=str(slot_id)).all()
            if slot_db_res:
                for slot in available_slots_copy:
                    block_slot = f'{search_date} {slot}'
                    now_dt = datetime.datetime.now()
                    expiry_at_unix = time.mktime(now_dt.timetuple())
                    for slot_res in slot_db_res:
                        if float(slot_res.expiry_at) > expiry_at_unix:
                            block_slot_dt = datetime.datetime.strptime(str(block_slot), '%Y-%m-%d %I:%M %p')
                            block_slot_dtf = block_slot_dt.strftime('%Y-%m-%d%I:%M %p')
                            if slot_res.block_slot == block_slot_dtf:
                                available_slots.remove(slot)

        # remove past slots
        available_slots_same_date = []
        if datetime.datetime.strptime(str(search_date), '%Y-%m-%d') == datetime.datetime.strptime(
                str(datetime.date.today()), '%Y-%m-%d'):
            now = datetime.datetime.now()
            current_time = now.strftime("%I:%M %p")
            for av_slot in available_slots:
                if datetime.datetime.strptime(str(av_slot), time_format) <= \
                        datetime.datetime.strptime(str(current_time), time_format):
                    continue
                else:
                    available_slots_same_date.append(str(av_slot))
            return available_slots_same_date

        return available_slots

    def search_clinic(self, search_view: SearchClinicView):
        search_date = None
        now = datetime.date.today()
        if search_view.city is None and (search_view.lat is None or search_view.lon is None):
            return None, f'location information not found'
        if search_view.search_date:
            search_date = str(search_view.search_date)
            if datetime.datetime.strptime(str(search_date), '%Y-%m-%d') < datetime.datetime.strptime(str(now),
                                                                                                     '%Y-%m-%d'):
                return None, f'old date'
        else:
            search_date = str(datetime.date.today())

        weekday_no = datetime.datetime.strptime(search_date, '%Y-%m-%d').weekday()
        day_name = calendar.day_name[weekday_no].lower()
        logger.info(f"the day name is : {day_name}")

        clinic_res_list = []
        mapping_dict = {}
        doctors_info = {}
        clinic_info = {}

        specialized_doctor = self.mongo_db['DoctorsInfo'].find({"specialization": str(search_view.specialist)})

        if len(list(specialized_doctor.clone())) == 0:
            return clinic_res_list, f'clinic not found because specialist not found'
        else:
            for doctor in specialized_doctor:
                map_query: DBClinicAndDoctors = self.db.query(DBClinicAndDoctors).filter_by(
                    doctorid=str(doctor['doctorid'])).all()
                if map_query is not None and doctor['is_active'] is True:
                    for mq in map_query:
                        slot_id_list = []
                        slot_res = self.mongo_db['DoctorAndClinic'].find_one(
                            {"mappingid": str(mq.mappingid)})
                        if len(slot_res[str(day_name)]) == 0:
                            continue
                        else:
                            for val in slot_res[str(day_name)]:
                                if val['availability_type'] == 'InClinic' and val['is_active'] is True \
                                        and datetime.datetime.strptime(str(search_date), '%Y-%m-%d') <= \
                                        datetime.datetime.strptime(str(val['ends_on']), '%Y-%m-%d %H:%M:%S'):
                                    doctor_res_duration: DBDoctor = self.db.query(DBDoctor).filter_by(
                                        doctorid=str(doctor['doctorid'])
                                    ).one_or_none()
                                    duration = doctor_res_duration.consulting_duration_clinic
                                    # Handle 85-minute consultation time to actually book a 90-minute slot
                                    if duration == 85:
                                        duration = 90
                                    slot_av = None
                                    slot_av = self.available_slot(doctorid=str(doctor['doctorid']),
                                                                  start_time=val['starts_at'],
                                                                  end_time=val['ends_at'],
                                                                  duration=duration,
                                                                  search_date=search_date,
                                                                  slot_id=val['slotid'])
                                    if len(slot_av) == 0:
                                        continue
                                    else:
                                        slot_id_list.append({'slotid': str(val['slotid']),
                                                             'starts_on': val['starts_on'],
                                                             'ends_on': val['ends_on'],
                                                             'starts_at': val['starts_at'],
                                                             'ends_at': val['ends_at'],
                                                             'available_slot': slot_av})
                                else:
                                    continue
                        if len(slot_id_list) != 0:
                            clinic_res: DBClinic
                            if search_view.city:
                                clinic_res = self.db.query(DBClinic).filter_by(clinicid=str(mq.clinicid)).filter_by(
                                    city=str(search_view.city)).one_or_none()
                            else:
                                clinic_res = self.db.query(DBClinic).filter_by(
                                    clinicid=str(mq.clinicid)).one_or_none()

                            if clinic_res is not None:
                                doctor_res: DBDoctor = self.db.query(DBDoctor).filter_by(
                                    doctorid=str(doctor['doctorid'])
                                ).one_or_none()
                                doctor_dict = {'doctorid': str(doctor_res.doctorid),
                                               'profilename': str(doctor['profilename']),
                                               'doctortype': str(doctor['doctortype']),
                                               'specialization': doctor['specialization'],
                                               'graduation': str(doctor['graduation']),
                                               'masters': str(doctor['masters']),
                                               'additional_qualification': str(doctor['additional_qualification']),
                                               'gender': str(doctor_res.gender),
                                               'languages': doctor['languages'], 'experience': doctor['experience'],
                                               'slot_info': slot_id_list}
                                clinic_exist = False
                                doctor_list = []
                                if len(clinic_res_list):
                                    for clinic_val_dict in clinic_res_list:
                                        if clinic_val_dict['clinicid'] == str(clinic_res.clinicid):
                                            doctor_list = clinic_val_dict['doctor_info']
                                            doctor_list.append(doctor_dict)
                                            clinic_val_dict['doctor_info'] = doctor_list
                                            clinic_exist = True
                                        else:
                                            continue
                                if not clinic_exist:
                                    doctor_list = []
                                    doctor_list.append(doctor_dict)
                                    clinic_dict = {'clinicid': str(clinic_res.clinicid),
                                                   "clinic_name": str(clinic_res.name),
                                                   "clinic_address": str(clinic_res.address),
                                                   "lat_lon": (float(clinic_res.lat), float(clinic_res.lon)),
                                                   "doctor_info": doctor_list}
                                    clinic_res_list.append(clinic_dict)
                            else:
                                continue
                        else:
                            continue
                else:
                    continue
        logger.info(clinic_res_list)
        if len(clinic_res_list) > 0:
            if search_view.city is None:
                for key, val in enumerate(clinic_res_list):
                    dist, expected_time, msg = get_distance_lat_lon(val['lat_lon'],
                                                                    (
                                                                        float(search_view.lat),
                                                                        float(search_view.lon)))
                    val['google_api_info'] = {'distnace': dist, 'time': expected_time}
                    clinic_res_list[key] = val
            return clinic_res_list, f'clinic found'
        else:
            return clinic_res_list, f'clinic not found'

    def date_day_after_increase(self, initial_date, increase_day):
        start_date = datetime.datetime.strptime(str(initial_date), "%Y-%m-%d")
        end_date = start_date + datetime.timedelta(days=increase_day)
        weekday_no = end_date.weekday()
        day_name = calendar.day_name[weekday_no].lower()
        return str(end_date), str(day_name)

    def search_all_clinic(self, search_view: SearchAllClinicView):
        search_date = str(datetime.date.today())
        if search_view.city is None and (search_view.lat is None or search_view.lon is None):
            return None, f'location information not found. Enter Lat Lon information or city name'
        weekday_no = datetime.datetime.strptime(search_date, '%Y-%m-%d').weekday()
        day_name = calendar.day_name[weekday_no].lower()
        logger.info(f"the start_date is : {str(search_date)} and day name is : {day_name}")

        clinic_res_list = []

        specialized_doctor = self.mongo_db['DoctorsInfo'].find({"$or": [
            {"specialization": search_view.specialist.title()},
            {"practice_area": search_view.specialist.title()}
        ]})

        if len(list(specialized_doctor.clone())) == 0:
            return clinic_res_list, f'clinic not found because specialist not found'
        else:
            for doctor in specialized_doctor:
                map_query: DBClinicAndDoctors = self.db.query(DBClinicAndDoctors).filter_by(
                    doctorid=str(doctor['doctorid'])).all()
                if map_query is not None and doctor['is_active'] is True:
                    for mq in map_query:

                        slot_id_list = []
                        slot_res = self.mongo_db['DoctorAndClinic'].find_one(
                            {"mappingid": str(mq.mappingid)})

                        # logger.info('\n\nslot res', slot_res, mq.mappingid)
                        for di in range(90):
                            increased_date, increased_day_name = self.date_day_after_increase(
                                initial_date=search_date,
                                increase_day=di)
                            date_ymd = increased_date.split(" ")[0]
                            if len(slot_res[str(increased_day_name)]) == 0:
                                continue
                            else:
                                # logger.info(slot_res[str(increased_day_name)])
                                for val in slot_res[str(increased_day_name)]:
                                    if val['availability_type'] == 'InClinic' and val['is_active'] is True \
                                            and datetime.datetime.strptime(str(date_ymd), '%Y-%m-%d') <= \
                                            datetime.datetime.strptime(str(val['ends_on']), '%Y-%m-%d %H:%M:%S'):
                                        doctor_res_duration: DBDoctor = self.db.query(DBDoctor).filter_by(
                                            doctorid=str(doctor['doctorid'])).one_or_none()
                                        duration = doctor_res_duration.consulting_duration_clinic
                                        # Handle 85-minute consultation time to actually book a 90-minute slot
                                        if duration == 85:
                                            duration = 90
                                        slot_av = None
                                        slot_av = self.available_slot(doctorid=str(doctor['doctorid']),
                                                                      start_time=val['starts_at'],
                                                                      end_time=val['ends_at'],
                                                                      duration=duration,
                                                                      search_date=date_ymd,
                                                                      slot_id=val['slotid'])
                                        if len(slot_av) == 0:
                                            continue
                                        else:
                                            slot_id_list.append({'slotid': str(val['slotid']),
                                                                 'starts_on': val['starts_on'],
                                                                 'ends_on': val['ends_on'],
                                                                 'starts_at': val['starts_at'],
                                                                 'ends_at': val['ends_at'],
                                                                 'available_slot': slot_av})
                                    else:
                                        continue
                            if len(slot_id_list) != 0:
                                clinic_res: DBClinic
                                if search_view.city:
                                    clinic_res = self.db.query(DBClinic).filter_by(
                                        clinicid=str(mq.clinicid)).filter_by(
                                        city=str(search_view.city)).one_or_none()
                                else:
                                    clinic_res = self.db.query(DBClinic).filter_by(
                                        clinicid=str(mq.clinicid)).one_or_none()

                                if clinic_res is not None:
                                    doctor_res: DBDoctor = self.db.query(DBDoctor).filter_by(
                                        doctorid=str(doctor['doctorid'])).one_or_none()
                                    doctor_dict = {'doctorid': str(doctor_res.doctorid),
                                                   'profilename': str(doctor['profilename']),
                                                   'doctortype': str(doctor['doctortype']),
                                                   'specialization': doctor['specialization'],
                                                   'graduation': str(doctor['graduation']),
                                                   'masters': str(doctor['masters']),
                                                   'additional_qualification': str(
                                                       doctor['additional_qualification']),
                                                   'gender': str(doctor_res.gender),
                                                   'languages': doctor['languages'],
                                                   'experience': doctor['experience'],
                                                   'slot_info': slot_id_list}
                                    clinic_exist = False
                                    doctor_list = []
                                    if len(clinic_res_list):
                                        for clinic_val_dict in clinic_res_list:
                                            logger.info(
                                                f"the clinicvaldict clinicid is :{str(clinic_val_dict['clinicid'])}")
                                            logger.info(f"the resonse clinicid is :{str(clinic_res.clinicid)}")
                                            if clinic_val_dict['clinicid'] == str(clinic_res.clinicid):
                                                doctor_list = clinic_val_dict['doctor_info']
                                                logger.info("matching clinic found")
                                                if datetime.datetime.strptime(
                                                        str(clinic_val_dict["available_slot_date"]
                                                            ), '%Y-%m-%d') == \
                                                        datetime.datetime.strptime(str(increased_date),
                                                                                   '%Y-%m-%d %H:%M:%S'):
                                                    # if clinic_val_dict["available_slot_date"] == increased_date:
                                                    doctor_list.append(doctor_dict)
                                                    clinic_val_dict['doctor_info'] = doctor_list
                                                    clinic_exist = True
                                                elif datetime.datetime.strptime(
                                                        str(clinic_val_dict["available_slot_date"]
                                                            ), '%Y-%m-%d') > \
                                                        datetime.datetime.strptime(
                                                            str(increased_date), '%Y-%m-%d %H:%M:%S'):
                                                    # elif clinic_val_dict["available_slot_date"] > increased_date:
                                                    logger.info(
                                                        f"initial date: {clinic_val_dict['available_slot_date']} and final date is : {increased_date}")
                                                    doctor_list = []
                                                    doctor_list.append(doctor_dict)
                                                    clinic_val_dict['doctor_info'] = doctor_list
                                                    clinic_exist = True
                                            else:
                                                continue
                                    if not clinic_exist:
                                        doctor_list = []
                                        doctor_list.append(doctor_dict)
                                        clinic_dict = {'clinicid': str(clinic_res.clinicid),
                                                       "clinic_name": str(clinic_res.name),
                                                       "clinic_address": str(clinic_res.address),
                                                       "lat_lon": (float(clinic_res.lat), float(clinic_res.lon)),
                                                       "city": str(clinic_res.city),
                                                       "available_slot_date": str(date_ymd),
                                                       "doctor_info": doctor_list}
                                        clinic_res_list.append(clinic_dict)
                                else:
                                    continue
                                break
                            else:
                                continue
                else:
                    continue
        # logger.info('clinic_res_list', clinic_res_list)
        clinic_res_list_lat_lon = []
        if len(clinic_res_list) > 0:
            if search_view.city is None:
                for key, val in enumerate(clinic_res_list):
                    logger.info(val['lat_lon'], )
                    dist, expected_time, msg = get_distance_lat_lon(val['lat_lon'],
                                                                    (
                                                                        float(search_view.lat),
                                                                        float(search_view.lon)
                                                                    )
                                                                    )
                    # logger.info(f"the distance is : {dist} and expected Time is : {expected_time} and msg is : {msg}")
                    if dist is not None:
                        if float(dist.split(" ")[0].replace(",", "").strip()) <= 50:
                            val['google_api_info'] = {'distance': dist, 'time': expected_time}
                            # clinic_res_list[key] = val
                            # val["available_slot_date"] = str(val["available_slot_date"].split(" ")[0])
                            clinic_res_list_lat_lon.append(val)
                    else:
                        continue

                if len(clinic_res_list_lat_lon) > 0:
                    return clinic_res_list_lat_lon, f'clinic found'
                else:
                    return None, 'clinic not found'
                # except Exception as e:
                #     return None, f'Error occurred as {str(e)} while getting clinics. Try searching on City'
            else:
                # clinic_res_list_city = []
                # for key, val in enumerate(clinic_res_list):
                #     val["available_slot_date"] = str(val["available_slot_date"].split(" ")[0])
                #     clinic_res_list_city.append(val)
                return clinic_res_list, 'clinic found'
        else:
            return clinic_res_list, f'clinic not found'

    def search_clinicid(self, search_view: SearchClinicIdView):
        if datetime.datetime.strptime(str(search_view.search_date), '%Y-%m-%d') < \
                datetime.datetime.strptime(str(datetime.date.today()), '%Y-%m-%d'):
            return None, f'old date'

        clinic_res: DBClinic
        clinic_res = self.db.query(DBClinic).filter_by(clinicid=str(search_view.clinicid)).one_or_none()
        if clinic_res is None:
            return None, 'clinicid not found'

        specialized_doctor = self.mongo_db['DoctorsInfo'].find({"specialization": str(search_view.specialist)})
        if len(list(specialized_doctor.clone())) == 0:
            return None, f'clinic not found because specialist not found'

        weekday_no = datetime.datetime.strptime(str(search_view.search_date), '%Y-%m-%d').weekday()
        day_name = calendar.day_name[weekday_no].lower()
        logger.info(f"the search_date is : {str(search_view.search_date)} and day name is : {day_name}")

        clinic_res_dict = {}

        for doctor in specialized_doctor:
            map_query: DBClinicAndDoctors = self.db.query(DBClinicAndDoctors).filter_by(
                doctorid=str(doctor['doctorid'])).filter_by(clinicid=str(search_view.clinicid)).all()
            if map_query is not None and doctor['is_active'] is True:
                for mq in map_query:
                    slot_id_list = []
                    slot_res = self.mongo_db['DoctorAndClinic'].find_one({"mappingid": str(mq.mappingid)})
                    if len(slot_res[str(day_name)]) == 0:
                        continue
                    else:
                        for val in slot_res[str(day_name)]:
                            if val['availability_type'] == 'InClinic' and val['is_active'] is True \
                                    and datetime.datetime.strptime(str(search_view.search_date), '%Y-%m-%d') <= \
                                    datetime.datetime.strptime(str(val['ends_on']), '%Y-%m-%d %H:%M:%S'):
                                doctor_res_duration: DBDoctor = self.db.query(DBDoctor).filter_by(
                                    doctorid=str(doctor['doctorid'])).one_or_none()
                                duration = doctor_res_duration.consulting_duration_clinic
                                # Handle 85-minute consultation time to actually book a 90-minute slot
                                if duration == 85:
                                    duration = 90
                                slot_av = None
                                slot_av = self.available_slot(doctorid=str(doctor['doctorid']),
                                                              start_time=val['starts_at'],
                                                              end_time=val['ends_at'],
                                                              duration=duration,
                                                              search_date=str(search_view.search_date),
                                                              slot_id=val['slotid'])
                                if len(slot_av) == 0:
                                    continue
                                else:
                                    slot_id_list.append({'slotid': str(val['slotid']),
                                                         'starts_on': val['starts_on'],
                                                         'ends_on': val['ends_on'],
                                                         'starts_at': val['starts_at'],
                                                         'ends_at': val['ends_at'],
                                                         'available_slot': slot_av})
                            else:
                                continue
                    if len(slot_id_list) != 0:
                        doctor_res: DBDoctor = self.db.query(DBDoctor).filter_by(
                            doctorid=str(doctor['doctorid'])
                        ).one_or_none()
                        doctor_dict = {'doctorid': str(doctor_res.doctorid),
                                       'profilename': str(doctor['profilename']),
                                       'doctortype': str(doctor['doctortype']),
                                       'specialization': doctor['specialization'],
                                       'graduation': str(doctor['graduation']),
                                       'masters': str(doctor['masters']),
                                       'additional_qualification': str(doctor['additional_qualification']),
                                       'gender': str(doctor_res.gender),
                                       'languages': doctor['languages'], 'experience': doctor['experience'],
                                       'slot_info': slot_id_list}
                        doctor_list = []
                        if len(clinic_res_dict):
                            doctor_list = clinic_res_dict['doctor_info']
                            doctor_list.append(doctor_dict)
                            clinic_res_dict['doctor_info'] = doctor_list
                        else:
                            doctor_list = []
                            doctor_list.append(doctor_dict)
                            clinic_res_dict = {'clinicid': str(clinic_res.clinicid),
                                               "clinic_name": str(clinic_res.name),
                                               "clinic_address": str(clinic_res.address),
                                               "lat_lon": (float(clinic_res.lat), float(clinic_res.lon)),
                                               "doctor_info": doctor_list}
                    else:
                        continue
            else:
                continue
        if clinic_res_dict:
            return clinic_res_dict, 'clinic found'
        else:
            return clinic_res_dict, 'sppointment slot is not available for this date and specialist'

    def get_clinic_doctor_availability(self, search_view: CheckDoctorAvailableSlot):
        try:
            if datetime.datetime.strptime(str(search_view.search_date), '%Y-%m-%d') < \
                    datetime.datetime.strptime(str(datetime.date.today()), '%Y-%m-%d'):
                return None, f'old date'

            doctor_res: DBDoctor
            doctor_res = self.db.query(DBDoctor).filter_by(doctorid=str(search_view.doctorid)).one_or_none()
            if doctor_res is None:
                return None, 'doctorid not found'

            clinic_res: DBClinic
            clinic_res = self.db.query(DBClinic).filter_by(clinicid=str(search_view.clinicid)).one_or_none()
            if clinic_res is None:
                return None, 'clinicid not found'

            mapping_res: DBClinicAndDoctors
            mapping_res = self.db.query(DBClinicAndDoctors).filter_by(clinicid=str(search_view.clinicid)).filter_by(
                doctorid=str(search_view.doctorid)).one_or_none()
            if mapping_res is None:
                return None, 'doctor is not mapped with this clinic'

            slot_id_list = []
            slot_res = self.mongo_db['DoctorAndClinic'].find_one({"mappingid": str(mapping_res.mappingid)})
            increased_date, increased_day_name = self.date_day_after_increase(
                initial_date=str(search_view.search_date),
                increase_day=0)
            date_ymd = increased_date.split(" ")[0]
            if len(slot_res[str(increased_day_name)]) == 0:
                return slot_id_list, 'No available slot on this date'
            else:
                for val in slot_res[str(increased_day_name)]:
                    if val['availability_type'] == 'InClinic' and val['is_active'] is True \
                            and datetime.datetime.strptime(str(date_ymd), '%Y-%m-%d') <= \
                            datetime.datetime.strptime(str(val['ends_on']), '%Y-%m-%d %H:%M:%S'):
                        # doctor_res_duration: DBDoctor = self.db.query(DBDoctor).filter_by(
                        #     doctorid=).one_or_none()
                        duration = doctor_res.consulting_duration_clinic
                        # Handle 85-minute consultation time to actually book a 90-minute slot
                        if duration == 85:
                            duration = 90
                        slot_av = None
                        slot_av = self.available_slot(doctorid=str(doctor_res.doctorid),
                                                      start_time=val['starts_at'],
                                                      end_time=val['ends_at'],
                                                      duration=duration,
                                                      search_date=date_ymd,
                                                      slot_id=val['slotid'])
                        if len(slot_av) == 0:
                            continue
                        else:
                            slot_id_list.append({'slotid': str(val['slotid']),
                                                 'starts_on': val['starts_on'],
                                                 'ends_on': val['ends_on'],
                                                 'starts_at': val['starts_at'],
                                                 'ends_at': val['ends_at'],
                                                 'available_slot': slot_av})
                    else:
                        continue
            if len(slot_id_list):
                return slot_id_list, 'Available Slot Found'
            else:
                return slot_id_list, 'Available Slot Not Found'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while getting doctor available slot for clinic'

    def add_patient_health_history(self, patient_view: PatientHealthHistoryView):
        patient_info = PatientHealthHistoryView(patient_id=patient_view.patient_id,
                                                common_allergies=patient_view.common_allergies,
                                                pre_existing_conditions=patient_view.pre_existing_conditions,
                                                family_history=patient_view.family_history,
                                                any_additional_information=patient_view.any_additional_information)
        try:
            check_info, msg = self.get_patient_health_history_user(patient_id=str(patient_view.patient_id))
            if not check_info:
                self.mongo_db['PatientHealthHistory'].insert_one(dict(patient_info))
            else:
                self.mongo_db['PatientHealthHistory'].find_one_and_update(
                    {"patient_id": str(patient_view.patient_id)},
                    {
                        "$set": dict(patient_info)})
            return dict(patient_info), 'Patient Health History Added Successfully'

        except Exception as e:
            err = str(e)
            return None, f'Internal Error code {err} for adding patient health history, with patient_id' \
                         f'{str(patient_view.patient_id)}'

    def get_patient_health_history(self, patient_view: GetPatientHealthHistoryView):
        patient_id = patient_view.patient_id
        try:
            patient_info = self.mongo_db['PatientHealthHistory'].find_one(dict(patient_id=patient_id))
            if patient_info:
                return patient_info, "Patient Record Found"
            else:
                return None, "Patient Record Not Found"

        except Exception as e:
            err = str(e)
            return None, f'Internal Error code {err} for getting patient health history, with patient_id' \
                         f'{str(patient_view.patient_id)}'

    def update_patient_health_history(self, patient_view: PatientHealthHistoryView):
        update_patient_info = PatientHealthHistoryView(patient_id=patient_view.patient_id,
                                                       common_allergies=patient_view.common_allergies,
                                                       pre_existing_conditions=patient_view.pre_existing_conditions,
                                                       family_history=patient_view.family_history,
                                                       any_additional_information=
                                                       patient_view.any_additional_information)
        try:
            self.mongo_db['PatientHealthHistory'].find_one_and_update({"patient_id": patient_view.patient_id}, {
                "$set": dict(update_patient_info)})
            return dict(update_patient_info), 'Patient Health History Updated Successfully'

        except Exception as e:
            err = str(e)
            return None, f'Internal Error code {err} for updating patient health history, with patient_id' \
                         f'{str(patient_view.patient_id)}'

    def add_feedback(self, add_feedback_view: PatientFeedbackForDoctor, user_id):
        try:
            appointment_res = self.mongo_db['Appointments'].find_one(dict(caseid=str(add_feedback_view.caseid)))
            if not appointment_res:
                return None, 'Invalid caseid'
            else:
                if appointment_res['patient_id'] != str(user_id) and appointment_res['booked_by'] != str(user_id):
                    return None, 'Invalid User'
            today_date = str(datetime.date.today())

            patient_feedback = PatientFeedbackForDoctorResponse(patientid=str(user_id),
                                                                doctorid=appointment_res['doctorid'],
                                                                caseid=str(add_feedback_view.caseid),
                                                                rating=add_feedback_view.rating,
                                                                date=today_date,
                                                                feedback=add_feedback_view.feedback \
                                                                    if add_feedback_view.feedback else "")
            self.mongo_db['PatientFeedbacks'].insert_one(dict(patient_feedback))
            return patient_feedback, 'Successfully added feedback'

        except Exception as e:
            return None, f'error occurred as {str(e)} while adding patient feedback'

    def get_feedback(self, get_feedback_view: GetDoctorFeedback):
        try:
            feedback_list = []
            feedback_res = self.mongo_db['PatientFeedbacks'].find(dict(doctorid=get_feedback_view.doctorid))
            if len(list(feedback_res.clone())) > 0:
                for val in feedback_res:
                    feedback_list.append({'patientid': val['patientid'], 'doctorid': val['doctorid'],
                                          'caseid': val['caseid'], 'rating': val['rating'], 'date': val['date'],
                                          'feedback': val['feedback']})
                return feedback_list, 'feedback found'
            else:
                return None, f'feedback not found for the doctorid: {str(get_feedback_view.doctorid)}'
        except Exception as e:
            return None, f'error occurred as {str(e)} while adding patient feedback'

    def get_patient_id_by_name(self, name):
        resp_user_list = self.db.query(dbmodels.DBUser).all()
        filtered_resp_user_list = [user for user in resp_user_list
                                   if name in f"{user.firstname} {user.lastname}"]
        li = []
        for res in filtered_resp_user_list:
            li.append(res.userid)
            # print(res.userid)
        return li

    def get_patient_id_by_mobile_number(self, mobile):
        resp_user_list = self.db.query(dbmodels.DBUser).filter(DBUser.mobile == mobile).all()
        filtered_resp_user_list = [user for user in resp_user_list
                                   if mobile in f"{user.mobile}"]
        li = []
        for res in filtered_resp_user_list:
            li.append(res.userid)
            # print(res.userid)
        return li

    def get_appointmets(self, get_appt_view: GetAppointmentView):

        doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)

        filters = {}
        patient_filter = {}
        sorting = {"created_at": -1}
        s3_instance = AWSS3Client()

        if (get_appt_view.sort):
            sorting = {}
            sort_column = get_appt_view.sort['column']
            sorting[sort_column] = get_appt_view.sort['sorting']

            if (sort_column == "status"):
                sorting = {
                    "status.status": get_appt_view.sort['sorting'], "created_at": -1}

        if get_appt_view.exclude_id:
            patient_filter['$ne'] = get_appt_view.exclude_id

        if get_appt_view.doc_id:
            filters['doctorid'] = get_appt_view.doc_id

        if get_appt_view.appointment_status:
            filters['status.status'] = get_appt_view.appointment_status

        if get_appt_view.appointment_id:
            filters['appointment_id'] = get_appt_view.appointment_id

        if get_appt_view.case_id:
            filters['caseid'] = {"$regex": get_appt_view.case_id}

        if get_appt_view.date:
            start_date = datetime.datetime.strptime(
                str(get_appt_view.date), '%Y-%m-%d')
            end_date = start_date + datetime.timedelta(days=1)
            filters['appointment_slot'] = {
                '$gt': start_date,
                '$lte': end_date
            }

        if (get_appt_view.appointment_status):
            filters['status.status'] = get_appt_view.appointment_status

        if get_appt_view.name:
            resp_user_list = self.db.query(DBUser).filter(
                and_(
                    or_(
                        DBUser.firstname.op('~*')(r'' + get_appt_view.name),
                        DBUser.lastname.op('~*')(r'' + get_appt_view.name)
                    ),
                    DBUser.is_deleted == False
                )
            ).all()
            patient_filter['$in'] = [user.userid for user in resp_user_list]

            resp_relative_list = self.db.query(DBRelatives).filter(or_(
                DBRelatives.firstname.op('~*')(r'' + get_appt_view.name),
                DBRelatives.lastname.op('~*')(r'' + get_appt_view.name))).all()
            patient_filter['$in'].extend([relative.relativeid for relative in resp_relative_list])

        if get_appt_view.mobile:
            resp_user_list = self.db.query(DBUser).filter(
                and_(
                    DBUser.mobile.op('~*')(r'[+]' + get_appt_view.mobile[1:]),
                    DBUser.is_deleted == False
                )
            ).all()
            patient_filter['$in'] = [user.userid for user in resp_user_list]

        if get_appt_view.patient_id:
            patient_filter = get_appt_view.patient_id

        if patient_filter != {}:
            if type(patient_filter) == dict:
                filters['$or'] = [{'patient_id': patient_filter}, {'patients': patient_filter}]
            else:
                filters['$or'] = [{'patient_id': patient_filter}, {'patients': patient_filter}]

        if "status.status" not in filters:
            filters["status.status"] = {"$ne": "Reschedule"}

        total_appointments = self.mongo_db['Appointments'].count_documents(filters)
        appointments = list(self.mongo_db['Appointments'].find(
            filter=filters,
            sort=list(sorting.items()),
            skip=get_appt_view.skip,
            limit=30,
            projection={'_id': False}
        ))
        

        all_doctors = set([x.get("doctorid", "") for x in appointments])
        # also including patients array
        all_patients = set()
        for appointment in appointments:
            patient_id = appointment.get("patient_id", "")
            if patient_id:
                all_patients.add(patient_id)
            patients_array = appointment.get("patients", [])
            if patients_array:
                all_patients.update(patients_array)
        all_clinics = set([x.get("clinicid", "") for x in appointments])

        doctor_data = {}
        patient_data = {}
        clinic_data = {}

        for doctor in all_doctors:
            doctor_data[doctor] = doctor_ctrl.get_by_id(doctorid=doctor)

        for patient in all_patients:
            patient_data[patient] = self.__get_patient_details(patientid=patient)

        for clinic in all_clinics:
            clinic_data[clinic] = doctor_ctrl.get_clinic_by_id(
                clinicid=clinic)

        all_appointment_ids = list(set([x.get("appointment_id") for x in appointments]))

        all_receipts = list(self.mongo_db["Receipt"].find({"appointment_id": {"$in": all_appointment_ids}}))
        receipt_dict = {x.get("appointment_id"): x.get("receipt_link") for x in all_receipts}

        all_meeting_codes = list(self.mongo_db['JitsiMeetInfo'].find({"appointment_id": {"$in": all_appointment_ids}}))
        meeting_code_dict = {x.get("appointment_id"): x.get("meeting_code") for x in all_meeting_codes}
        all_payments = list(self.mongo_db['Paymentgateway3'].find({"appointment_id": {"$in": all_appointment_ids}}))
        promo_dict = {x.get("appointment_id"): x.get("promo_code") for x in all_payments}
        mode_dict = {x.get("appointment_id"): x.get("payment_mode") for x in all_payments}

        for appointment in appointments:
            appointment['doctor_name'] = None
            appointment['patient_name'] = None
            appointment['clinic_name'] = None
            appointment['clinic_address'] = None

            doctor_details = doctor_data[appointment["doctorid"]]
            appointment["patient_otp"] = meeting_code_dict.get(appointment.get("appointment_id", None))
            appointment["receipt_url"] = s3_instance.get_presigned_url(
                receipt_dict.get(appointment.get("appointment_id")))
            appointment["promo_code"] = promo_dict.get(appointment.get("appointment_id"))
            appointment["payment_method"] = mode_dict.get(appointment.get("appointment_id"))

            if doctor_details[0]:
                doctor = doctor_details[0]
                name = str(doctor['firstname']) + " " + str(doctor['lastname'])
                appointment['doctor_name'] = name

            if appointment["patient_id"]:
                patients_array = appointment.get("patients", [])
                if patients_array and len(patients_array) > 1:
                    # Handle multiple patients
                    patient_names = []
                    patients_details_array = []
                    for patient_id in patients_array:
                        patient_detail = patient_data.get(patient_id)
                        if patient_detail:
                            patient_names.append(f"{patient_detail['firstname']} {patient_detail['lastname']}")
                            patients_details_array.append(patient_detail)
                    
                    appointment['patient_name'] = ", ".join(patient_names)
                    appointment['patients_details'] = patients_details_array
                else:
                    # Handle single patient (original logic)
                    patients_details = patient_data[appointment["patient_id"]]
                    if patients_details:
                        patient_name = f"{patients_details['firstname']} {patients_details['lastname']}"
                        appointment['patient_name'] = patient_name
                        appointment['patients_details'] = [patients_details]

            if appointment["clinicid"]:
                clinic_details = clinic_data[appointment["clinicid"]]

                if clinic_details:
                    appointment['clinic_name'] = clinic_details.name
                    appointment['clinic_address'] = clinic_details.address

        if total_appointments:
            return appointments, '', total_appointments
        else:
            return [], 'No appointments', 0

    def send_appointment_cancellation_mail_to_admin(self, appointment_details: AppointmentBookingDetails,
                                                    other_info: AppointmentConfirmationToAdmin):
        try:
            # from api_configs import current_env
            logger.info(current_env)
            if current_env.rstrip() != 'prod':
                logger.info("The current env is {}".format(current_env))
                return
            logger.info("Current Env {}".format(current_env))
            aws_mail_ctrl = AWSEmailAndMsgSender()
            text_local_controller = TextLocalController()

            ayoo_admin: dbmodels.DBAdmin = self.db.query(dbmodels.DBAdmin).filter(
                dbmodels.DBAdmin.email == '<EMAIL>').one_or_none()
            logger.info(ayoo_admin)
            booked_by = 'Admin' if appointment_details.booked_by == ayoo_admin.userid else 'Self'
            fees_info = self.mongo_db['Paymentgateway3'].find_one(
                dict(appointment_id=appointment_details.appointment_id), {'amount': 1})
            fees = fees_info if fees_info is not None else other_info.doctor_details[
                'consulting_fees_clinic'] if appointment_details.appointment_type == 'InClinic' else \
                other_info.doctor_details['consulting_fees_virtual']

            booked_for = appointment_details.appointment_for.name if appointment_details.appointment_for.name == 'Self' else f'{other_info.patient_name} {(appointment_details.appointment_for.name)}'
            client_name = other_info.patient_name if other_info.booking_person_name == '' else other_info.booking_person_name
            appointment_status = 'cancelled'
            msg_for_admin = f'''
Dear Admin,
The following appointment has been {appointment_status}.
The details are as follows:
- Client's Name: {client_name}
- Session Date: {other_info.appointment_date}
- Session Time: {other_info.appointment_time}
- Provider: {other_info.doctor_name}
- Appointment Type: {str(appointment_details.appointment_type.name)}
- Total Cost: {fees}
- Booking Timestamp: {appointment_details.created_at}
- Booked By: {booked_by}
- Booked For : {booked_for}
Best regards,
Engineering Team
            '''
            phone_text_for_admin = f'Appointment {appointment_status} for {other_info.patient_name} on {other_info.appointment_date} at {other_info.appointment_time} with {other_info.doctor_name}'
            email_subject = f'{appointment_status.title()} Consultation Booking: {other_info.patient_name}- {other_info.appointment_date}, {other_info.appointment_time}'
            logger.info(phone_text_for_admin)
            logger.info(email_subject)
            aws_mail_ctrl.send_appointment_confirmation(meeting_message=msg_for_admin,
                                                        mobile=ayoo_admin.mobile,
                                                        email=ayoo_admin.email,
                                                        subject=email_subject,
                                                        phone_text_for_admin=phone_text_for_admin)

            if str(appointment_details.appointment_type.name) == 'Virtual':
                text_local_controller.send_sms(template_name=f'AppointmentCancelAdminVirtual',
                                               var_list=[other_info.patient_name,
                                                         f'{other_info.appointment_date}, {other_info.appointment_time}',
                                                         other_info.doctor_name], numbers=ayoo_admin.mobile)
            else:
                text_local_controller.send_sms(template_name=f'AppointmentCancelAdminInClinic', var_list=[
                    f'{other_info.appointment_date}, {other_info.appointment_time}', other_info.doctor_name],
                                               numbers=ayoo_admin.mobile)


        except Exception as e:
            logger.info(str(e))

    def handle_notification_for_cancelled_appointment(self, appointment_id: str):
        try:
            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            appointment_data = self.mongo_db['Appointments'].find_one({'appointment_id': appointment_id})
            if appointment_data is None:
                raise Exception('Invalid appointment ID')

            appointment_details = AppointmentBookingDetails(
                appointment_id=appointment_id,
                appointment_type=appointment_data['appointment_type'],
                appointment_for=appointment_data['appointment_for'],
                children_included=appointment_data['children_included'],
                symptoms=appointment_data['symptoms'],
                symptoms_audio_clip=appointment_data['symptoms_audio_clip'],
                additional_notes=appointment_data['additional_notes'],
                clinicid=appointment_data['clinicid'],
                doctorid=appointment_data['doctorid'],
                doctor_name=appointment_data['doctor_name'] if 'doctor_name' in appointment_data else '',
                appointment_slot=appointment_data['appointment_slot'],
                is_active=appointment_data['is_active'],
                is_confirmed=appointment_data['is_confirmed'],
                payment=appointment_data['payment'],
                caseid=appointment_data['caseid'],
                patient_id=appointment_data['patient_id'],
                patients=appointment_data['patients'],
                booked_by=appointment_data['booked_by'],
                created_at=appointment_data['created_at'],
                end_date=appointment_data['end_date'],
                case_open=appointment_data['case_open'],
                follow_up_type=appointment_data['follow_up_type'] if 'follow_up_type' in appointment_data else 'Paid',
                care_type=appointment_data['care_type'] if 'care_type' in appointment_data else 'Mental Health'
            )

            jitsi_link = 'None'

            if appointment_details.appointment_type == 'Virtual':
                jitsi_ctrl = JitsiMeetController(db=self.db, mongo=self.mongo)
                meeting_info = jitsi_ctrl.check_meeting_using_appointment_id(
                    appointment_id=str(appointment_id))
                if meeting_info is None:
                    raise Exception('Could not find meeting details')

                jitsi_link = meeting_info['meeting_link']

            patient_id = appointment_details.patient_id
            patient_details = self.__get_patient_details(patientid=patient_id)
            patient_name = ''
            if patient_details:
                patient_name = patient_details['firstname'] + ' ' + patient_details['lastname']
            service_provider = {}
            doctor_details, msg = doctor_ctrl.get_by_id(doctorid=appointment_details.doctorid)
            if doctor_details is not None:
                service_provider = {
                    'doctor_firstname': doctor_details.get('firstname'),
                    'doctor_lastname': doctor_details.get('lastname'),
                    'graduation': doctor_details.get('graduation'),
                    'masters': doctor_details.get('masters'),
                    'specialization': doctor_details.get('specialization'),
                    'specialization_field': doctor_details.get('specialization_field'),
                    'mobile': doctor_details.get('mobile'),
                    'profilename': doctor_details.get('profilename')
                }

            if appointment_details.appointment_for == 'Self':
                user_id = patient_id
            else:
                caretaker_details = self.db.query(DBRelatives).filter(
                    DBRelatives.relativeid == patient_id).first()
                if caretaker_details is None:
                    user_id = patient_id
                else:
                    user_id = caretaker_details.caretaker_id

            frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)
            appointment_cancel_notif = AppointmentEvent(appointment_id=appointment_id,
                                                        user_id=user_id,
                                                        user_name=self.__get_patient_details(patientid=user_id)[
                                                            'firstname'],
                                                        appointment_for=appointment_details.appointment_for,
                                                        patient_id=patient_id,
                                                        patient_name=patient_name,
                                                        doctor_id=appointment_details.doctorid,
                                                        doctor_name=service_provider['doctor_firstname'],
                                                        clinic_id=appointment_details.clinicid,
                                                        clinic_name=self.__get_clinic_details(
                                                            appointment_details.clinicid),
                                                        event_date=appointment_details.appointment_slot,
                                                        event_type=appointment_details.appointment_type,
                                                        jitsi_link=jitsi_link)
            frbs_ctrl.appointment_cancel(appointment_event=appointment_cancel_notif)

            appointment_date = appointment_details.appointment_slot.strftime("%d %B %Y")
            appointment_time = appointment_details.appointment_slot.strftime("%I:%M %p")

            doctor_name = service_provider.get('doctor_firstname', '') + ' ' + service_provider.get('doctor_lastname',
                                                                                                    '')
            doctor_specialization = service_provider.get('specialization', '')

            other_info = AppointmentConfirmationToAdmin(appointment_id=appointment_id,
                                                        doctor_name=doctor_name,
                                                        doctor_specialization=doctor_specialization,
                                                        doctor_details=doctor_details,
                                                        patient_name=patient_name,
                                                        appointment_date=appointment_date,
                                                        appointment_time=appointment_time,
                                                        booking_person_name='',
                                                        is_rescheduled_appointment=False
                                                        )

            text_local_controller = TextLocalController()

            appointment_type = str(appointment_details.appointment_type.name)

            if appointment_type == 'InClinic':
                var_list_doctor = [patient_name, f'{appointment_date}, {appointment_time}']
                var_list_patient = [patient_name, service_provider.get('profilename'),
                                    f'{appointment_date}, {appointment_time}']

            else:
                var_list_doctor = [service_provider.get('profilename'), patient_name,
                                   f'{appointment_date}, {appointment_time}']
                var_list_patient = [patient_name, service_provider.get('profilename'),
                                    f'{appointment_date}, {appointment_time}']

            text_local_controller.send_sms(template_name=f'AppointmentCancelDoctor{appointment_type}',
                                           var_list=var_list_doctor, numbers=service_provider.get('mobile'))

            text_local_controller.send_sms(template_name=f'AppointmentCancelPatient{appointment_type}',
                                           var_list=var_list_patient, numbers=patient_details.get('mobile'))

            self.send_appointment_cancellation_mail_to_admin(appointment_details=appointment_details,
                                                             other_info=other_info)

        except Exception as e:
            logger.info(f'Error occurred while handling notification for appointment cancellation: {str(e)}')
            raise Exception(f'Error occurred while handling notification for appointment cancellation: {str(e)}')

    def update_appointment_status(self, app_view: UpdateAppointmentStatus, next_appointment_details=None):
        try:
            valid_appointment_id = self.mongo_db['Appointments'].find_one({"appointment_id": app_view.appointment_id})
            if valid_appointment_id is None:
                raise Exception('Invalid appointment ID')
            status = {"status": app_view.status, "reason": app_view.reason, "comment": app_view.comment}

            if next_appointment_details is not None:
                status["next_appointment_id"] = next_appointment_details.get("appointment_id")

            status_list = ["Cancelled", "cancelled", 'Reschedule', 'Rescheduled']

            is_active_status = False if app_view.status in status_list else True
            is_confirmed_status = False if app_view.status in status_list else True

            self.mongo_db['Notifications'].update_many({"object_id": app_view.appointment_id},
                                                       {
                                                           "$set": {"notification_status": True,
                                                                    "object_active_status": False}
                                                       })
            if app_view.status in ["Cancelled", "cancelled"]:
                #print('1')
                self.handle_notification_for_cancelled_appointment(appointment_id=app_view.appointment_id)
                #print(2)

            self.mongo_db['Appointments'].find_one_and_update({"appointment_id": app_view.appointment_id},
                                                              {"$set": {
                                                                  "status": status,
                                                                  "is_active": is_active_status,
                                                                  "is_confirmed": is_confirmed_status
                                                              }})

            return f'appointment_id{app_view.appointment_id} status : {status} updated successfully'
        except Exception as e:
            print(str(e))
            raise Exception(str(e))

    def add_appointment_status(self, app_view: appointmentstatus):

        resp = app_view.status
        for res in resp:
            status_id = str(uuid.uuid4())
            self.mongo_db['Appointment_status'].insert_one(dict(status_id=status_id, status=res))
        return "Status recoreded successfully"

    def list_appointment_status(self):
        resp = list(self.mongo_db['Appointment_status'].find())
        if resp:
            li = []
            for res in resp:
                dr = {}
                for key, values in res.items():
                    if key != '_id':
                        dr[key] = values
                li.append(dr)

            # print("result")
            # print(li)
            return li
        return "No records found"

    def delete_appointment_status(self, app_view: appointmentstatusdel):
        self.mongo_db['Appointment_status'].find_one_and_delete({"status_id": app_view.status_id})
        return "Status deleted successfully"

    def add_appointment_reason(self, app_view: appointmentreason):
        resp = app_view.reason
        for res in resp:
            reason_id = str(uuid.uuid4())
            self.mongo_db['Appointment_reason'].insert_one(dict(reason_id=reason_id, reason=res))
        return "reason recoreded successfully"

    def list_appointment_reason(self):
        resp = list(self.mongo_db['Appointment_reason'].find())
        if resp:
            li = []
            for res in resp:
                dr = {}
                for key, values in res.items():
                    if key != '_id':
                        dr[key] = values
                li.append(dr)

            # print("result")
            # print(li)
            return li
        else:
            return "No records found"

    def delete_appointment_reason(self, app_view: appointmentreasondel):
        self.mongo_db['Appointment_reason'].find_one_and_delete({"reason_id": app_view.reason_id})
        return "reason deleted successfully"

    def block_appointment(self, slotBlock_viewmodel: SlotBlockingView):
        created_at = datetime.datetime.now()
        expiry_at = datetime.datetime.now() + datetime.timedelta(minutes=15)
        expiry_at_unix = time.mktime(expiry_at.timetuple())
        logger.info(f'the type of expiry_at_unix is : {type(expiry_at_unix)}')
        dt_string = slotBlock_viewmodel.block_slot
        dt_datetime = datetime.datetime.strptime(dt_string, '%Y-%m-%d %I:%M %p')
        dt_datetimef = dt_datetime.strftime("%Y-%m-%d%I:%M %p")
        db_block = DBBlockSlots(block_id=str(uuid.uuid4()), slot_id=slotBlock_viewmodel.slot_id,
                                block_slot=dt_datetimef,
                                doctor_id=slotBlock_viewmodel.doctor_id, created_at=created_at,
                                expiry_at=expiry_at_unix)
        try:
            self.db.add(db_block)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, '', f'DB Error code {err} for blocking appointment'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, '', f'Internal error code {err} for blocking appointment'

        resp = SlotBlockingResponse(slot_id=slotBlock_viewmodel.slot_id, created_at=created_at,
                                    expiry_at=expiry_at_unix)
        return dict(resp), 'appointment blocked'

    def check_appointment_slot(self, check_model: SlotBlockingView):
        try:
            block_slot_dt = datetime.datetime.strptime(str(check_model.block_slot), '%Y-%m-%d %I:%M %p')
            block_slot_dtf = block_slot_dt.strftime('%Y-%m-%d%I:%M %p')
            now_dt = datetime.datetime.now()
            expiry_at_unix = time.mktime(now_dt.timetuple())
            if block_slot_dt < now_dt:
                return None, "old datetime"

            booked_slot = self.mongo_db['Appointments'].find_one({"doctorid": str(check_model.doctor_id),
                                                                  "appointment_slot": block_slot_dt})
            if booked_slot:
                return 'booked', 'appointment already booked'

            slot_db_res: DBBlockSlots
            slot_db_res = self.db.query(DBBlockSlots).filter_by(doctor_id=str(check_model.doctor_id)).filter_by(
                slot_id=str(check_model.slot_id)).filter_by(block_slot=block_slot_dtf).all()
            if slot_db_res:
                for slot_res in slot_db_res:
                    if float(slot_res.expiry_at) > expiry_at_unix:
                        return 'blocked', 'appointment already blocked'
            return 'free', 'appointment is currently free'
        except Exception as e:
            return None, f'error occurred as {str(e)} while getting status of slot {str(check_model.block_slot)}'

    def delete_appointment_by_id(self, appointment_id: str):
        try:
            app = self.mongo_db["Appointments"].find_one({"appointment_id": appointment_id})
            app_obj = copy.deepcopy(app)
            self.mongo_db["Appointments"].delete_one({"appointment_id": appointment_id})

            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            doctor_details, msg = doctor_ctrl.get_by_id(
                doctorid=app_obj['doctorid'])

            frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)
            appointment_notif = AppointmentEvent(appointment_id=app_obj['appointment_id'],
                                                 user_id="None",
                                                 user_name="Admin",
                                                 appointment_for=app_obj['appointment_for'],
                                                 patient_id=app_obj['patient_id'],
                                                 patient_name=
                                                 self.__get_patient_details(patientid=app_obj['patient_id'])[
                                                     'firstname'],
                                                 doctor_id=app_obj['doctorid'],
                                                 doctor_name=doctor_details['firstname'],
                                                 clinic_id=app_obj['clinicid'],
                                                 clinic_name=self.__get_clinic_details(app_obj['clinicid']),
                                                 event_date=app_obj['appointment_slot'],
                                                 event_type=app_obj['appointment_type'])
            frbs_ctrl.appointment_cancel(appointment_event=appointment_notif)

            return app_obj, 'Appointment deleted successfully'
        except Exception as e:
            return None, f'Exception occurred as {str(e)} while deleting Appointment by admin'

    def update_appointment_by_id(self, update_appt_view=AppointmentBookingDetailsCopy1):

        appointment_id = update_appt_view.appointment_id

        appt_slot = datetime.datetime.strptime(str(update_appt_view.appointment_slot), '%Y-%m-%d %I:%M %p')
        appt_info = AppointmentBookingDetailsCopy(appointment_id=appointment_id,
                                                  appointment_type=update_appt_view.appointment_type,
                                                  appointment_for=update_appt_view.appointment_for,
                                                  symptoms=update_appt_view.symptoms,
                                                  symptoms_audio_clip=update_appt_view.symptoms_audio_clip,
                                                  additional_notes=update_appt_view.additional_notes,
                                                  patient_id=update_appt_view.patient_id,
                                                  clinicid=update_appt_view.clinicid,
                                                  doctorid=update_appt_view.doctorid,
                                                  appointment_slot=appt_slot,
                                                  is_active=update_appt_view.is_active,
                                                  is_confirmed=update_appt_view.is_confirmed,
                                                  payment=update_appt_view.payment,
                                                  booked_by=update_appt_view.booked_by)

        appt_info_to_dict = dict(appt_info)

        try:
            mongo_collection = self.mongo_db['Appointments']
            doctor_id_before = (mongo_collection.find_one({"appointment_id": appointment_id}))['doctorid']
            mongo_collection.find_one_and_update({"appointment_id": appointment_id}, {"$set": appt_info_to_dict})
        except Exception as e:
            err = str(e)
            return None, f'Internal Error code mongo - {err} for updating appointment, with appointment_id {str(update_appt_view.appointment_id)}'

        doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
        doctor_details, msg = doctor_ctrl.get_by_id(
            doctorid=appt_info_to_dict['doctorid'])

        frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)
        appointment_notif = AppointmentEvent(appointment_id=appt_info_to_dict['appointment_id'],
                                             user_id="None",
                                             user_name="Admin",
                                             appointment_for=appt_info_to_dict['appointment_for'],
                                             patient_id=appt_info_to_dict['patient_id'],
                                             patient_name=
                                             self.__get_patient_details(patientid=appt_info_to_dict['patient_id'])[
                                                 'firstname'],
                                             doctor_id=appt_info_to_dict['doctorid'],
                                             doctor_name=doctor_details['firstname'],
                                             clinic_id=appt_info_to_dict['clinicid'],
                                             clinic_name=self.__get_clinic_details(appt_info_to_dict['clinicid']),
                                             event_date=appt_info_to_dict['appointment_slot'],
                                             event_type=appt_info_to_dict['appointment_type'],
                                             remarks=doctor_id_before)
        frbs_ctrl.appointment_update(appointment_event=appointment_notif)

        return appt_info_to_dict, 'Appointment updated'

    def get_all_appointments(self):
        try:
            appointment_list = []
            appointment_res = self.mongo_db['Appointments'].find()
            if appointment_res:
                for appointment in appointment_res:
                    appointment_list.append({'appointment_id': appointment["appointment_id"],
                                             'appointment_type': appointment["appointment_type"],
                                             'appointment_for': appointment["appointment_for"],
                                             'symptoms': appointment["symptoms"],
                                             'clinicid': appointment["clinicid"],
                                             'doctorid': appointment["doctorid"],
                                             'appointment_slot': appointment["appointment_slot"],
                                             'caseid': appointment["caseid"],
                                             'patient_id': self.__get_patient_details(appointment['patient_id']),
                                             'booked_by': self.__get_patient_details(appointment['booked_by'])
                                             })
            if len(appointment_list):
                return appointment_list, "Appointment found"
            else:
                return appointment_list, "No Appointment"
        except Exception as e:
            return None, f'Error occurred as {str(e)} while getting all appointments'

    def get_patient_vitals(self, user_id: str):
        try:
            check_info_exist = self.mongo_db['Vitals'].find_one({"userid": str(user_id)})
            if check_info_exist:
                return check_info_exist, 'user vitals data found'
            else:
                return check_info_exist, 'user vitals data not found'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while getting vitals for userid: {user_id}'

    def insert_patient_vitals(self, patient_vitals: PatientVitalsInfoDate, user_id: str):
        try:
            check_info_exist, msg = self.get_patient_vitals(user_id=str(user_id))
            inserted_data = {}
            if check_info_exist:
                if patient_vitals.heart_rate_readings:
                    heart_dict = {"reading_id": str(uuid.uuid4()),
                                  "heart_rate": patient_vitals.heart_rate_readings.heart_rate,
                                  "reading_date": datetime.datetime.strptime(str(
                                      patient_vitals.heart_rate_readings.reading_date), '%Y-%m-%d %I:%M%p') if
                                  patient_vitals.heart_rate_readings.reading_date else datetime.datetime.today()}
                    inserted_data['heart_rate'] = heart_dict
                    check_info_exist['heart_rate'].append(heart_dict)

                if patient_vitals.weight_readings:
                    weight_dict = {"reading_id": str(uuid.uuid4()),
                                   "weight": patient_vitals.weight_readings.weight,
                                   "reading_date": datetime.datetime.strptime(str(
                                       patient_vitals.weight_readings.reading_date), '%Y-%m-%d %I:%M%p') if
                                   patient_vitals.weight_readings.reading_date else datetime.datetime.today()}
                    inserted_data['weight'] = weight_dict
                    check_info_exist['weight'].append(weight_dict)

                if patient_vitals.blood_pressure_readings:
                    bp_dict = {"reading_id": str(uuid.uuid4()),
                               "systole": patient_vitals.blood_pressure_readings.systole,
                               "diastole": patient_vitals.blood_pressure_readings.diastole,
                               "reading_date": datetime.datetime.strptime(str(
                                   patient_vitals.blood_pressure_readings.reading_date), '%Y-%m-%d %I:%M%p') if
                               patient_vitals.blood_pressure_readings.reading_date else datetime.datetime.today()}
                    inserted_data['blood_pressure'] = bp_dict
                    check_info_exist['blood_pressure'].append(bp_dict)

                if patient_vitals.height_readings:
                    height_dict = {"reading_id": str(uuid.uuid4()),
                                   "height": patient_vitals.height_readings.height,
                                   "reading_date": datetime.datetime.strptime(str(
                                       patient_vitals.height_readings.reading_date), '%Y-%m-%d %I:%M%p') if
                                   patient_vitals.height_readings.reading_date else datetime.datetime.today()}
                    inserted_data['height'] = height_dict
                    check_info_exist['height'].append(height_dict)

                if patient_vitals.temperature_readings:
                    temp_dict = {"reading_id": str(uuid.uuid4()),
                                 "temperature": patient_vitals.temperature_readings.temperature,
                                 "reading_date": datetime.datetime.strptime(str(
                                     patient_vitals.temperature_readings.reading_date), '%Y-%m-%d %I:%M%p') if
                                 patient_vitals.temperature_readings.reading_date else datetime.datetime.today()}
                    inserted_data['temperature'] = temp_dict
                    check_info_exist['temperature'].append(temp_dict)

                if patient_vitals.hba1c_readings:
                    hb_dict = {"reading_id": str(uuid.uuid4()),
                               "temperature": patient_vitals.hba1c_readings.hba1c,
                               "reading_date": datetime.datetime.strptime(str(
                                   patient_vitals.hba1c_readings.reading_date), '%Y-%m-%d %I:%M%p') if
                               patient_vitals.hba1c_readings.reading_date else datetime.datetime.today()}
                    inserted_data['hba1c'] = hb_dict
                    check_info_exist['hba1c'].append(hb_dict)

                self.mongo_db['Vitals'].find_one_and_update({"userid": str(user_id)}, {
                    "$set": dict(check_info_exist)
                })
                return inserted_data, 'inserted user vitals info successfully'
            else:
                data_to_insert = {}
                data_to_insert["userid"] = str(user_id)
                data_to_insert["heart_rate"] = []
                data_to_insert["weight"] = []
                data_to_insert["blood_pressure"] = []
                data_to_insert["height"] = []
                data_to_insert["temperature"] = []
                data_to_insert["hba1c"] = []
                if patient_vitals.heart_rate_readings:
                    heart_dict = {"reading_id": str(uuid.uuid4()),
                                  "heart_rate": patient_vitals.heart_rate_readings.heart_rate,
                                  "reading_date": datetime.datetime.strptime(str(
                                      patient_vitals.heart_rate_readings.reading_date), '%Y-%m-%d %I:%M%p') if
                                  patient_vitals.heart_rate_readings.reading_date else datetime.datetime.today()}
                    inserted_data['heart_rate'] = heart_dict
                    data_to_insert["heart_rate"].append(heart_dict)

                if patient_vitals.weight_readings:
                    weight_dict = {"reading_id": str(uuid.uuid4()),
                                   "weight": patient_vitals.weight_readings.weight,
                                   "reading_date": datetime.datetime.strptime(str(
                                       patient_vitals.weight_readings.reading_date), '%Y-%m-%d %I:%M%p') if
                                   patient_vitals.weight_readings.reading_date else datetime.datetime.today()}
                    inserted_data['weight'] = weight_dict
                    data_to_insert["weight"].append(weight_dict)

                if patient_vitals.blood_pressure_readings:
                    bp_dict = {"reading_id": str(uuid.uuid4()),
                               "systole": patient_vitals.blood_pressure_readings.systole,
                               "diastole": patient_vitals.blood_pressure_readings.diastole,
                               "reading_date": datetime.datetime.strptime(str(
                                   patient_vitals.blood_pressure_readings.reading_date), '%Y-%m-%d %I:%M%p') if
                               patient_vitals.blood_pressure_readings.reading_date else datetime.datetime.today()}
                    inserted_data['blood_pressure'] = bp_dict
                    data_to_insert["blood_pressure"].append(bp_dict)

                if patient_vitals.height_readings:
                    height_dict = {"reading_id": str(uuid.uuid4()),
                                   "height": patient_vitals.height_readings.height,
                                   "reading_date": datetime.datetime.strptime(str(
                                       patient_vitals.height_readings.reading_date), '%Y-%m-%d %I:%M%p') if
                                   patient_vitals.height_readings.reading_date else datetime.datetime.today()}
                    inserted_data['height'] = height_dict
                    data_to_insert["height"].append(height_dict)

                if patient_vitals.temperature_readings:
                    temp_dict = {"reading_id": str(uuid.uuid4()),
                                 "temperature": patient_vitals.temperature_readings.temperature,
                                 "reading_date": datetime.datetime.strptime(str(
                                     patient_vitals.temperature_readings.reading_date), '%Y-%m-%d %I:%M%p') if
                                 patient_vitals.temperature_readings.reading_date else datetime.datetime.today()}
                    inserted_data['temperature'] = temp_dict
                    data_to_insert["temperature"].append(temp_dict)

                if patient_vitals.hba1c_readings:
                    hb_dict = {"reading_id": str(uuid.uuid4()),
                               "temperature": patient_vitals.hba1c_readings.hba1c,
                               "reading_date": datetime.datetime.strptime(str(
                                   patient_vitals.hba1c_readings.reading_date), '%Y-%m-%d %I:%M%p') if
                               patient_vitals.hba1c_readings.reading_date else datetime.datetime.today()}
                    inserted_data['hba1c'] = hb_dict
                    data_to_insert["hba1c"].append(hb_dict)

                self.mongo_db['Vitals'].insert_one(data_to_insert)
                return inserted_data, 'inserted user vitals info successfully'

        except Exception as e:
            return None, f'Error occurred as {str(e)} while inserting vitals for userid: {user_id}'

    def update_patient_vitals(self, patient_vitals: PatientVitalsInfoDateReadingId, user_id: str):
        try:
            check_info_exist, msg = self.get_patient_vitals(user_id=str(user_id))
            if check_info_exist:
                updated_data = {}
                if patient_vitals.heart_rate_readings:
                    if len(check_info_exist['heart_rate']):
                        for index_val in range(len(check_info_exist['heart_rate'])):
                            if check_info_exist['heart_rate'][index_val]['reading_id'] == \
                                    str(patient_vitals.heart_rate_readings.reading_id):
                                check_info_exist['heart_rate'][index_val]['heart_rate'] = \
                                    patient_vitals.heart_rate_readings.heart_rate if \
                                        patient_vitals.heart_rate_readings.heart_rate else \
                                        check_info_exist['heart_rate'][index_val]['heart_rate']
                                check_info_exist['heart_rate'][index_val]['reading_date'] = \
                                    datetime.datetime.strptime(patient_vitals.heart_rate_readings.reading_date,
                                                               '%Y-%m-%d %I:%M%p') if \
                                        patient_vitals.heart_rate_readings.reading_date else \
                                        check_info_exist['heart_rate'][index_val]['reading_date']
                                updated_data['heart_rate'] = check_info_exist['heart_rate'][index_val]
                                break
                            else:
                                continue
                        if 'heart_rate' not in updated_data.keys():
                            return None, 'reading_id not found for heart rate'
                    else:
                        return None, 'heart_rate data does not exists in database'

                if patient_vitals.weight_readings:
                    if len(check_info_exist['weight']):
                        for index_val in range(len(check_info_exist['weight'])):
                            if check_info_exist['weight'][index_val]['reading_id'] == \
                                    str(patient_vitals.weight_readings.reading_id):
                                check_info_exist['weight'][index_val]['weight'] = \
                                    patient_vitals.weight_readings.weight if \
                                        patient_vitals.weight_readings.weight else \
                                        check_info_exist['weight'][index_val]['weight']
                                check_info_exist['weight'][index_val]['reading_date'] = \
                                    datetime.datetime.strptime(patient_vitals.weight_readings.reading_date,
                                                               '%Y-%m-%d %I:%M%p') if \
                                        patient_vitals.weight_readings.reading_date else \
                                        check_info_exist['weight'][index_val]['reading_date']
                                updated_data['weight'] = check_info_exist['weight'][index_val]
                                break
                            else:
                                continue
                        if 'weight' not in updated_data.keys():
                            return None, 'reading_id not found for weight'
                    else:
                        return None, 'weight data does not exists in database'

                if patient_vitals.blood_pressure_readings:
                    if len(check_info_exist['blood_pressure']):
                        for index_val in range(len(check_info_exist['blood_pressure'])):
                            if check_info_exist['blood_pressure'][index_val]['reading_id'] == \
                                    str(patient_vitals.blood_pressure_readings.reading_id):
                                check_info_exist['blood_pressure'][index_val]['systole'] = \
                                    patient_vitals.blood_pressure_readings.systole if \
                                        patient_vitals.blood_pressure_readings.systole else \
                                        check_info_exist['blood_pressure'][index_val]['systole']
                                check_info_exist['blood_pressure'][index_val]['diastole'] = \
                                    patient_vitals.blood_pressure_readings.diastole if \
                                        patient_vitals.blood_pressure_readings.diastole else \
                                        check_info_exist['blood_pressure'][index_val]['diastole']
                                check_info_exist['blood_pressure'][index_val]['reading_date'] = \
                                    datetime.datetime.strptime(patient_vitals.blood_pressure_readings.reading_date,
                                                               '%Y-%m-%d %I:%M%p') if \
                                        patient_vitals.blood_pressure_readings.reading_date else \
                                        check_info_exist['blood_pressure'][index_val]['reading_date']
                                updated_data['blood_pressure'] = check_info_exist['blood_pressure'][index_val]
                                break
                            else:
                                continue
                        if 'blood_pressure' not in updated_data.keys():
                            return None, 'reading_id not found for blood pressure'
                    else:
                        return None, 'blood_pressure data does not exists in database'

                if patient_vitals.height_readings:
                    if len(check_info_exist['height']):
                        for index_val in range(len(check_info_exist['height'])):
                            logger.info(check_info_exist['height'][index_val]['reading_id'])
                            if check_info_exist['height'][index_val]['reading_id'] == \
                                    str(patient_vitals.height_readings.reading_id):
                                check_info_exist['height'][index_val]['height'] = \
                                    patient_vitals.height_readings.height if \
                                        patient_vitals.height_readings.height else \
                                        check_info_exist['height'][index_val]['height']
                                check_info_exist['height'][index_val]['reading_date'] = \
                                    datetime.datetime.strptime(patient_vitals.height_readings.reading_date,
                                                               '%Y-%m-%d %I:%M%p') if \
                                        patient_vitals.height_readings.reading_date else \
                                        check_info_exist['height'][index_val]['reading_date']
                                updated_data['height'] = check_info_exist['height'][index_val]
                                break
                            else:
                                continue
                        if 'height' not in updated_data.keys():
                            return None, 'reading_id not found for height'
                    else:
                        return None, 'heart_rate data does not exists in database'

                if patient_vitals.temperature_readings:
                    if len(check_info_exist['temperature']):
                        for index_val in range(len(check_info_exist['temperature'])):
                            if check_info_exist['temperature'][index_val]['reading_id'] == \
                                    str(patient_vitals.heart_rate_readings.reading_id):
                                check_info_exist['temperature'][index_val]['temperature'] = \
                                    patient_vitals.temperature_readings.temperature if \
                                        patient_vitals.temperature_readings.temperature else \
                                        check_info_exist['temperature'][index_val]['temperature']
                                check_info_exist['temperature'][index_val]['reading_date'] = \
                                    datetime.datetime.strptime(patient_vitals.temperature_readings.reading_date,
                                                               '%Y-%m-%d %I:%M%p') if \
                                        patient_vitals.temperature_readings.reading_date else \
                                        check_info_exist['temperature'][index_val]['reading_date']
                                updated_data['temperature'] = check_info_exist['heart_rate'][index_val]
                                break
                            else:
                                continue
                        if 'temperature' not in updated_data.keys():
                            return None, 'reading_id not found for temperature'
                    else:
                        return None, 'temperature data does not exists in database'

                if patient_vitals.hba1c_readings:
                    if len(check_info_exist['hba1c']):
                        for index_val in range(len(check_info_exist['hba1c'])):
                            if check_info_exist['hba1c'][index_val]['reading_id'] == \
                                    str(patient_vitals.hba1c_readings.reading_id):
                                check_info_exist['hba1c'][index_val]['hba1c'] = \
                                    patient_vitals.hba1c_readings.hba1c if \
                                        patient_vitals.hba1c_readings.hba1c else \
                                        check_info_exist['hba1c'][index_val]['hba1c']
                                check_info_exist['hba1c'][index_val]['reading_date'] = \
                                    datetime.datetime.strptime(patient_vitals.hba1c_readings.reading_date,
                                                               '%Y-%m-%d %I:%M%p') if \
                                        patient_vitals.hba1c_readings.reading_date else \
                                        check_info_exist['hba1c'][index_val]['reading_date']
                                updated_data['hba1c'] = check_info_exist['hba1c'][index_val]
                                break
                            else:
                                continue
                        if 'hba1c' not in updated_data.keys():
                            return None, 'reading_id not found for hba1c'
                    else:
                        return None, 'heart_rate data does not exists in database'
                self.mongo_db['Vitals'].find_one_and_update({"userid": str(user_id)}, {
                    "$set": check_info_exist
                })
                return updated_data, "updated successfully"
            else:
                return None, 'database not exists'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while updating vitals for userid: {user_id}'

    def get_members_appointments_new(self, userid: str, payload_dict: dict, request_data: RequestAppointmentList):
        try:
            # is_permission = payload_dict['permission']
            # if is_permission == True:
            #     logger.info("here")

            appointments = []
            docs_new = []
            if request_data.starts_from:
                date_from = datetime.datetime.strptime(
                    request_data.starts_from, '%Y-%m-%d')
            else:
                date_from = None

            if request_data.till:
                date_till = datetime.datetime.strptime(
                    request_data.till, '%Y-%m-%d')
            else:
                date_till = date_from + \
                            datetime.timedelta(hours=+22, minutes=+59, seconds=+59)

            mongo_collection_appointments = self.mongo_db['Appointments']
            appointments_list = list(mongo_collection_appointments.find(
                {
                    '$or': [
                        {'patient_id': userid},
                        {'booked_by': userid}
                    ],
                    '$and': [
                        {'appointment_slot': {'$gte': date_from, '$lte': date_till}},
                        {'is_active': True},
                        {'is_confirmed': True}
                    ]
                }
            )
            )
            if appointments_list:
                doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
                for docs in appointments_list:
                    patient = self.__get_patient_details(
                        patientid=docs['patient_id'])
                    clinic = None
                    doctor_details = None
                    get_doctor_details, msg = doctor_ctrl.get_by_id(
                        doctorid=docs['doctorid'])
                    if get_doctor_details is not None:
                        doctor_details = {
                            'name': get_doctor_details['firstname'] + ' ' + get_doctor_details['lastname'],
                            'languages': get_doctor_details['languages'],
                            'graduation': get_doctor_details['graduation'],
                            'masters': get_doctor_details['masters'],
                            'doctortype': get_doctor_details['doctortype'],
                            'specialization': get_doctor_details['specialization'],
                            'specialization_field': get_doctor_details['specialization_field'],
                            'additional_qualification': get_doctor_details['additional_qualification'],
                            'bio': get_doctor_details['bio']
                        }
                    if docs['clinicid']:
                        clinic_details = doctor_ctrl.get_clinic_by_id(
                            clinicid=docs['clinicid'])
                        if clinic_details:
                            clinic = clinic_details.name
                    docs_new.append(docs)
                    # apps_new = sorted(docs_new, key = lambda i: i['appointment_slot'])
                    # for apps in apps_new:
                    # Appending jitsi meeting link info in the response
                    meeting_link_info = ''
                    if docs['appointment_type'] == 'Virtual':
                        jitsi_ctrl = JitsiMeetController(db=self.db, mongo=self.mongo)
                        meeting_info = jitsi_ctrl.check_meeting_using_appointment_id(
                            appointment_id=str(docs['appointment_id']))
                        meeting_link_info = meeting_info['meeting_link']

                    appointments.append(
                        dict(ResponseMemberAppointmentListCopy(
                            appointment_id=docs['appointment_id'],
                            caseid=docs['caseid'],
                            symptoms=docs['symptoms'],
                            symptoms_audio_clip=docs['symptoms_audio_clip'],
                            additional_notes=docs['additional_notes'],
                            appointment_slot=docs['appointment_slot'],
                            appointment_type=docs['appointment_type'],
                            appointment_for=docs['appointment_for'],
                            patient=patient,
                            clinic=clinic,
                            doctor=doctor_details,
                            meeting_link=meeting_link_info,
                            booking_cost=docs['payment']
                        ))
                    )
            if len(appointments):
                resp = sorted(appointments, key=lambda i: i['appointment_slot'])
                return resp, 'appointments found'
            else:
                return None, 'No appointments'
        except Exception as e:
            raise HTTPException(status_code=401, detail=str(e))

    def get_patient_prescriptions(self, case_id: str):
        try:
            check_info_exist = self.mongo_db['PatientPrescription'].find_one({"caseid": str(case_id)})
            if check_info_exist:
                return check_info_exist, 'user prescriptions data found'
            else:
                return check_info_exist, 'user prescriptions data not found'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while getting prescriptions for caseid: {case_id}'

    def upload_patient_prescription(self, add_pres_view: UploadPrescriptionRequestView, user_id: str):
        try:
            appointment_res = self.mongo_db['Appointments'].find_one(dict(caseid=str(add_pres_view.caseid)))
            if not appointment_res:
                return None, 'Invalid caseid'
            else:
                if appointment_res['patient_id'] != str(user_id) and appointment_res['booked_by'] != str(user_id):
                    return None, 'Invalid User'
            # patient_prescription
            patient_pres_res, mesg = self.get_patient_prescriptions(case_id=str(add_pres_view.caseid))
            if patient_pres_res:
                prescription_list = patient_pres_res['prescriptions']
                for prescription in prescription_list:
                    if prescription['prescription_name'] == str(add_pres_view.prescription_name):
                        return None, f'{str(add_pres_view.prescription_name)} already exists for caseid {str(add_pres_view.caseid)}'
            logger.info('prescriptions not there')
            prescription_id_create = str(uuid.uuid4())
            s3_instance = AWSS3Client()
            generated_url, msg = s3_instance.upload_prescription_to_s3(
                image_str=str(add_pres_view.prescription_image_encoded), image_id=str(prescription_id_create))
            if generated_url is None:
                return None, msg

            today_date = datetime.datetime.today()
            prescription_date = datetime.datetime.strptime(str(add_pres_view.prescription_date), '%Y-%m-%d')
            add_pres_info = PrescriptionView(prescription_id=str(prescription_id_create),
                                             prescription_name=str(add_pres_view.prescription_name),
                                             prescription_image_url=str(generated_url),
                                             prescription_date=prescription_date,
                                             prescription_upload_date=today_date,
                                             prescription_update_date=today_date)
            logger.info(f'the add_prescription_info is : {add_pres_info}')
            prescription_list = []
            if patient_pres_res:
                prescription_list = patient_pres_res['prescriptions']
                prescription_list.append(dict(add_pres_info))
                # patient_report_res['reports'] = reports_list
                self.mongo_db['PatientPrescription'].find_one_and_update({"caseid": str(add_pres_view.caseid)}, {
                    "$set": dict(caseid=str(add_pres_view.caseid), prescriptions=prescription_list)})
            else:
                self.mongo_db['PatientPrescription'].insert_one(dict(caseid=str(add_pres_view.caseid),
                                                                     prescriptions=[dict(add_pres_info)]))
            return dict(add_pres_info), "Prescription Successfully Uploaded"
        except Exception as e:
            return None, f'Internal Error code {str(e)} for adding prescription for caseid {str(add_pres_view.caseid)}'

    def update_patient_prescription(self, update_pres_view: UpdatePrescriptionRequestView, user_id: str):
        try:
            appointment_res = self.mongo_db['Appointments'].find_one(dict(caseid=str(update_pres_view.caseid)))
            if not appointment_res:
                return None, 'Invalid caseid'
            else:
                if appointment_res['patient_id'] != str(user_id) and appointment_res['booked_by'] != str(user_id):
                    return None, 'Invalid User'

            patient_pres_res, mesg = self.get_patient_prescriptions(case_id=str(update_pres_view.caseid))
            if patient_pres_res:
                prescription_list = patient_pres_res['prescriptions']
                status_check = False
                updated_prescription = None
                for prescription in prescription_list:
                    if prescription['prescription_id'] == str(update_pres_view.prescription_id):
                        status_check = True
                        prescription[
                            'prescription_name'] = update_pres_view.prescription_name if update_pres_view.prescription_name else \
                            prescription['prescription_name']
                        prescription['prescription_date'] = datetime.datetime.strptime(
                            str(update_pres_view.prescription_date),
                            '%Y-%m-%d') if update_pres_view.prescription_date else prescription['prescription_date']
                        prescription['prescription_update_date'] = datetime.datetime.today()
                        if update_pres_view.prescription_image_encoded:
                            s3_instance = AWSS3Client()
                            generated_url, msg = s3_instance.upload_prescription_to_s3(
                                image_str=str(update_pres_view.prescription_image_encoded),
                                image_id=str(prescription['prescription_id']))
                            prescription['prescription_image_url'] = generated_url
                        updated_prescription = prescription
                if status_check:
                    self.mongo_db['PatientPrescription'].find_one_and_update(
                        {"caseid": str(update_pres_view.caseid)}, {
                            "$set": dict(caseid=str(update_pres_view.caseid), prescriptions=prescription_list)})
                    return updated_prescription, 'Successfully Updated'
                else:
                    return None, f"prescription_id: {update_pres_view.prescription_id} not found for the caseid : " \
                                 f"{update_pres_view.caseid}"
            else:
                return None, f'caseid: {str(update_pres_view.caseid)} not found'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while updating patient rescription with ' \
                         f'caseid {str(update_pres_view.caseid)}'

    def delete_patient_prescription(self, delete_pres_view: DeletePrescriptionRequestView, user_id: str):
        try:
            appointment_res = self.mongo_db['Appointments'].find_one(dict(caseid=str(delete_pres_view.caseid)))
            if not appointment_res:
                return None, 'Invalid caseid'
            else:
                if appointment_res['patient_id'] != str(user_id) and appointment_res['booked_by'] != str(user_id):
                    return None, 'Invalid User'

            patient_pres_res, mesg = self.get_patient_prescriptions(case_id=str(delete_pres_view.caseid))
            if patient_pres_res:
                prescription_list = patient_pres_res['prescriptions']
                status_check = False
                final_list = []
                deleted_pres = None
                for prescription in prescription_list:
                    if prescription['prescription_id'] == str(delete_pres_view.prescription_id):
                        status_check = True
                        deleted_pres = prescription
                    else:
                        final_list.append(prescription)
                if status_check:
                    self.mongo_db['PatientPrescription'].find_one_and_update(
                        {"caseid": str(delete_pres_view.caseid)}, {
                            "$set": dict(caseid=str(delete_pres_view.caseid), prescriptions=final_list)})
                    return deleted_pres, 'Successfully Deleted'
                else:
                    return None, f"prescription_id: {delete_pres_view.prescription_id} not found for the caseid : " \
                                 f"{delete_pres_view.caseid}"
            else:
                return None, f'caseid: {str(delete_pres_view.caseid)} not found'
        except Exception as e:
            return None, f'Error occurred as {str(e)} while deleting patient prescription with ' \
                         f'caseid {str(delete_pres_view.caseid)}'

    def get_patient_health_history_user(self, patient_id: str):
        try:
            patient_info = self.mongo_db['PatientHealthHistory'].find_one(dict(patient_id=patient_id))
            if patient_info:
                return patient_info, "Patient Record Found"
            else:
                return None, "Patient Record Not Found"

        except Exception as e:
            err = str(e)
            return None, f'Internal Error code {err} for getting patient health history, with patient_id' \
                         f'{str(patient_id)}'

    def add_patient_health_history_user(self, patient_view: PatientHealthHistoryViewUser, patient_id: str):
        patient_info = PatientHealthHistoryView(patient_id=patient_id,
                                                common_allergies=patient_view.common_allergies,
                                                pre_existing_conditions=patient_view.pre_existing_conditions,
                                                family_history=patient_view.family_history,
                                                any_additional_information=patient_view.any_additional_information)
        try:
            check_info, msg = self.get_patient_health_history_user(patient_id=patient_id)
            if not check_info:
                self.mongo_db['PatientHealthHistory'].insert_one(dict(patient_info))
            else:
                self.mongo_db['PatientHealthHistory'].find_one_and_update({"patient_id": patient_id}, {
                    "$set": dict(patient_info)})
            return dict(patient_info), 'Patient Health History Added Successfully'

        except Exception as e:
            err = str(e)
            return None, f'Internal Error code {err} for adding patient health history, with patient_id' \
                         f'{str(patient_id)}'

    def update_patient_health_history_user(self, patient_view: PatientHealthHistoryViewUser, patient_id: str):
        update_patient_info = PatientHealthHistoryView(patient_id=patient_id,
                                                       common_allergies=patient_view.common_allergies,
                                                       pre_existing_conditions=patient_view.pre_existing_conditions,
                                                       family_history=patient_view.family_history,
                                                       any_additional_information=
                                                       patient_view.any_additional_information)
        try:
            self.mongo_db['PatientHealthHistory'].find_one_and_update({"patient_id": patient_id}, {
                "$set": dict(update_patient_info)})
            return dict(update_patient_info), 'Patient Health History Updated Successfully'

        except Exception as e:
            err = str(e)
            return None, f'Internal Error code {err} for updating patient health history, with patient_id' \
                         f'{str(patient_id)}'

    def get_family_policy_from_familyid(self, FamilyID: str):
        try:
            family_policy_info = self.mongo_db['FamilyPolicy'].find_one(dict(FamilyID=FamilyID))
            if family_policy_info:
                return family_policy_info, "Family policy Record Found"
            else:
                return None, "Family policy Record Not Found"

        except Exception as e:
            err = str(e)
            return None, f'Internal Error code {err} for getting Family policy, with patient_id' \
                         f'{str(FamilyID)}'

    def create_family_policy(self, create_family_policy_view: AddFamilyView):
        try:
            if create_family_policy_view.FamilyID:
                userid__list = create_family_policy_view.FamilyMembersUserIds

                Family_perms_data_list = []

                # i and j are userids in userid_list

                for i in userid__list:
                    primary_member_perm_data_list = []

                    for j in userid__list:
                        if j is not i:

                            relative_user_id = str(j)
                            if relative_user_id:
                                relative_perm_data_dict = RelativePermData(RelativeUserId=relative_user_id,
                                                                           RelativeAPIPermsData={},
                                                                           RelationType='')
                            else:
                                continue
                            primary_member_perm_data_list.append(dict(relative_perm_data_dict))
                        else:
                            continue
                    primary_user_id = str(i)
                    if primary_user_id:
                        primary_member_policy_data_dict = PrimaryMemberPolicyData(
                            PrimaryMemberUserId=primary_user_id,
                            PrimaryMemberPermData=primary_member_perm_data_list)
                        Family_perms_data_list.append(dict(primary_member_policy_data_dict))

                    else:
                        continue
                resp = AddFamilyResponse(FamilyMembersUserIds=create_family_policy_view.FamilyMembersUserIds,
                                         FamilyID=create_family_policy_view.FamilyID,
                                         FamilyPermsData=Family_perms_data_list)

                check_info, msg = self.get_family_policy_from_familyid(FamilyID=create_family_policy_view.FamilyID)
                if not check_info:

                    self.mongo_db['FamilyPolicy'].insert_one(dict(resp))
                else:
                    self.mongo_db['FamilyPolicy'].find_one_and_update(
                        {"FamilyID": create_family_policy_view.FamilyID},
                        {
                            "$set": dict(resp)})

                return resp, f'Family policy created sucessfully'

            else:
                return None, f'Please enter a valid Family ID'

        except Exception as e:
            return None, f'Internal Error code {str(e)} while adding family'

    # use base health controller as object
    def check_existing_open_case(self, patient_id: str, doctor_id: str):
        try:
            val = self.mongo_db['Appointments'].find_one({"$and": [
                {"patient_id": str(patient_id)},
                {"doctorid": str(doctor_id)},
                {"is_active": True}]})
            return val['caseid'] if val else None
        except Exception as e:
            return str(e)

    def append_end_date_field(self):
        try:
            all_appointment = self.mongo_db['Appointments'].find()
            if len(list(all_appointment.clone())):
                for val in all_appointment:
                    val['end_date'] = val['appointment_slot'] + datetime.timedelta(days=10)
                    self.mongo_db['Appointments'].find_one_and_update({"appointment_id": val["appointment_id"]}, {
                        "$set": dict(val)
                    })
                return 1, "successfully updated"
            else:
                return None, "data not found"
        except Exception as e:
            return None, str(e)

    def generate_family_code(self, userid: str):
        try:
            N = 6
            code_gen = ''.join(random.choices(string.ascii_uppercase +
                                              string.digits, k=N))
            created_at = datetime.datetime.now()
            expiry_at = datetime.datetime.now() + datetime.timedelta(days=1)
            expiry_at_unix = time.mktime(expiry_at.timetuple())
            unique_code = str(code_gen)
            # logger.info(f'the type of expiry_at_unix is : {type(expiry_at_unix)}')
            db_family_code = DBFamilyCode(user_id=userid,
                                          unique_code=unique_code,
                                          created_at=created_at,
                                          expiry_at=expiry_at_unix)
            try:
                self.db.add(db_family_code)
                self.db.commit()

            except sqlalchemy.exc.SQLAlchemyError as d:
                self.db.rollback()
                err = str(d)
                return None, '', f'DB Error code {err} for generating code'

            except BaseException as e:
                self.db.rollback()
                err = str(e)
                return None, '', f'Internal error code {err} for generating code'

            resp = FamilyCodeGen(user_id=userid,
                                 unique_code=unique_code,
                                 created_at=created_at,
                                 expiry_at=expiry_at_unix)
            return dict(resp), 'unique code generated'
        except Exception as e:
            return None, str(e)

    def related_realtion_id(self, relation_id: str):
        try:
            tmp_relation: DBRelatives = self.db.query(DBRelatives).filter(
                DBRelatives.relation_id == relation_id).one_or_none()
            related_relation: DBRelatives = self.db.query(DBRelatives).filter(
                DBRelatives.caretaker_id == tmp_relation.relativeid, DBRelatives.relativeid == tmp_relation.caretaker_id
            ).one_or_none()
            if related_relation:
                return related_relation.relation_id
            else:
                return None
        except:
            return None

    def generate_caretaker_access_code(self, userid: str, relation_id: str, consent_duration: str):
        try:
            N = 6
            code_gen = ''.join(random.choices(string.ascii_uppercase +
                                              string.digits, k=N))
            created_at = datetime.datetime.now()
            expiry_at = datetime.datetime.now() + datetime.timedelta(days=1)
            expiry_at_unix = time.mktime(expiry_at.timetuple())
            unique_code = str(code_gen)
            realted_realtion_id = self.related_realtion_id(relation_id)
            if not realted_realtion_id:
                return None, "No Related Relation Found"
            # logger.info(f'the type of expiry_at_unix is : {type(expiry_at_unix)}')
            db_family_code = DBCaretakerAccessCode(user_id=userid,
                                                   unique_code=unique_code,
                                                   relation_id=realted_realtion_id,
                                                   consent_duration=int(consent_duration),
                                                   created_at=created_at,
                                                   expiry_at=expiry_at_unix)
            try:
                self.db.add(db_family_code)
                self.db.commit()

            except sqlalchemy.exc.SQLAlchemyError as d:
                self.db.rollback()
                err = str(d)
                return None, '', f'DB Error code {err} for generating code'

            except BaseException as e:
                self.db.rollback()
                err = str(e)
                return None, '', f'Internal error code {err} for generating code'

            resp = CaretakerAccessCodeGen(user_id=userid,
                                          unique_code=unique_code,
                                          relation_id=relation_id,
                                          consent_duration=consent_duration,
                                          created_at=created_at,
                                          expiry_at=expiry_at_unix)
            return dict(resp), 'unique code generated'
        except Exception as e:
            return None, str(e)

    def add_member_to_family(self, request_data: AddMemberToFamilyView, user_id: str):
        try:
            logged_in_userid = user_id
            incoming_userid = request_data.relative_id
            logged_in_usr: DBFamilyUsers = self.db.query(dbmodels.DBFamilyUsers).filter_by(
                user_id=logged_in_userid).one_or_none()
            incoming_usr: DBFamilyUsers = self.db.query(dbmodels.DBFamilyUsers).filter_by(
                user_id=incoming_userid).one_or_none()

            if logged_in_usr:
                logged_in_familyid = str(logged_in_usr.family_id)
            else:
                logged_in_familyid = None
            if incoming_usr:
                incoming_user_famliyid = str(incoming_usr.family_id)
            else:
                incoming_user_famliyid = None

            if not logged_in_familyid and not incoming_user_famliyid:
                # create new familyid and append to both
                members = []
                new_familyid = str(uuid.uuid4())

                resp_incoming = DBFamilyUsers(user_id=incoming_userid,
                                              family_id=new_familyid)
                resp_logged = DBFamilyUsers(user_id=logged_in_userid,
                                            family_id=new_familyid)
                try:
                    self.db.add(resp_incoming)
                    self.db.add(resp_logged)
                    self.db.commit()

                except sqlalchemy.exc.SQLAlchemyError as d:
                    self.db.rollback()
                    err = str(d)
                    return None, '', f'DB Error code {err} for adding family member'

                except BaseException as e:
                    self.db.rollback()
                    err = str(e)
                    return None, '', f'Internal error code {err} for adding family member'
                members.append(incoming_userid)
                members.append(logged_in_userid)
                resp = AddFamilyIDRelativeResponse(family_id=new_familyid,
                                                   members=members)

                return resp, 'relative added to family sucessfully'

            elif logged_in_familyid and not incoming_user_famliyid:
                # append the family id of incoming user to the 'to be added user'
                members = []
                common_familyid = logged_in_usr.family_id

                resp_incoming = DBFamilyUsers(user_id=incoming_userid,
                                              family_id=common_familyid)
                try:
                    self.db.add(resp_incoming)
                    self.db.commit()

                except sqlalchemy.exc.SQLAlchemyError as d:
                    self.db.rollback()
                    err = str(d)
                    return None, '', f'DB Error code {err} for adding family member'

                except BaseException as e:
                    self.db.rollback()
                    err = str(e)
                    return None, '', f'Internal error code {err} for adding family member'
                members.append(incoming_userid)
                members.append(logged_in_userid)
                resp = AddFamilyIDRelativeResponse(family_id=common_familyid,
                                                   members=members)

                return resp, 'relative added to family sucessfully'

            elif incoming_user_famliyid and not logged_in_familyid:
                # append family id from incoming user to logged in
                members = []
                common_familyid = incoming_usr.family_id

                resp_logged_in = DBFamilyUsers(user_id=logged_in_userid,
                                               family_id=common_familyid)
                try:
                    self.db.add(resp_logged_in)
                    self.db.commit()

                except sqlalchemy.exc.SQLAlchemyError as d:
                    self.db.rollback()
                    err = str(d)
                    return None, '', f'DB Error code {err} for adding family member'

                except BaseException as e:
                    self.db.rollback()
                    err = str(e)
                    return None, '', f'Internal error code {err} for adding family member'
                members.append(incoming_userid)
                members.append(logged_in_userid)
                resp = AddFamilyIDRelativeResponse(family_id=common_familyid,
                                                   members=members)

                return resp, 'relative added to family sucessfully'

            elif incoming_user_famliyid and logged_in_familyid:
                # merge family case
                return None, 'Merge case, both members have own family IDs'

        except Exception as e:
            return None, str(e)

    def add_relative_to_family(self, request_data: AddRelativeToFamilyView, userid: str):
        code_dict = {}
        family_code = request_data.relative_code
        logged_in_to_incoming_user_relation = request_data.relation_type

        if not family_code:
            return None, f'Please enter a valid relative code '
        code_query: DBFamilyCode = self.db.query(DBFamilyCode).filter_by(unique_code=str(family_code)).all()

        now = datetime.datetime.now()
        now_in_unix = time.mktime(now.timetuple())

        if not code_query:
            return None, f'Invalid code, please try again'

        for i in code_query:
            code_dict['relative_userid'] = i.user_id
            code_dict['expiry_at'] = i.expiry_at

        relative_userid = str(code_dict['relative_userid'])

        if float(now_in_unix) > float(code_dict['expiry_at']):
            return None, f'the expiry code, {family_code} has expired'

        family_query: DBFamilies = self.db.query(DBFamilies).filter_by(created_by=userid).all()

        if family_query is None:
            return None, 'Family details not found'
        family_dict = {}

        for family in family_query:
            family_dict['family_key'] = family.familiy_key
            family_dict['familyid'] = family.familyid
            family_dict['created_at'] = family.created_at
            family_dict['created_by'] = family.created_by

        family_id = str(family_dict['familyid'])

        relative_query: DBFamilyUserMapping = self.db.query(DBFamilyUserMapping).filter_by(familyid=family_id).all()
        if relative_query is None:
            return None, 'Relative details not found'

        family_member_dict = {}
        family_member_list = []

        for family_members in relative_query:
            family_member_dict['mapping_id'] = family_members.mappingid
            family_member_dict['userid'] = family_members.userid
            family_member_dict['familyid'] = family_members.familyid
            family_member_dict['date_added'] = family_members.date_added
            family_member_list.append(copy.deepcopy(family_member_dict))

        family_member_userids_list = []

        for family_member in family_member_list:
            family_member_userids_list.append(family_member['userid'])

        if relative_userid in family_member_userids_list:
            return None, f'person with userid {relative_userid} is already in your family'

        new_mapping_in = str(uuid.uuid4())

        if relative_userid not in family_member_userids_list:
            resp_family_incoming = DBFamilyUserMapping(mappingid=new_mapping_in,
                                                       userid=relative_userid,
                                                       familyid=family_id,
                                                       date_added=now)

            add_relation_resp, msg = self.add_relations(logged_in_userid=userid,
                                                        incoming_userid=relative_userid,
                                                        relation_type=str(logged_in_to_incoming_user_relation))

            try:
                self.db.add(resp_family_incoming)
                self.db.commit()

            except sqlalchemy.exc.SQLAlchemyError as d:
                self.db.rollback()
                err = str(d)
                return None, '', f'DB Error code {err} for creating new family'

            except BaseException as e:
                self.db.rollback()
                err = str(e)
                return None, '', f'Internal error code {err} for creating new family'

        final_family_resp = CreateNewRelativeResponse(mappingid=new_mapping_in,
                                                      userid=userid,
                                                      familyid=family_id,
                                                      added_on=str(now))

        return final_family_resp, f'Relative added sucessfully with relations as {add_relation_resp}'

    def create_new_family(self, request_data: CreateNewFamilyView, userid: str):
        try:
            if not request_data.relative_code:
                return None, f'Please enter a relative code'

            code_dict = {}
            unique_relative_code = request_data.relative_code
            relation_type = request_data.relation_type
            code_query: DBFamilyCode = self.db.query(DBFamilyCode).filter_by(
                unique_code=str(unique_relative_code)).all()

            if not code_query:
                return None, f'Invalid code, please try again'

            for i in code_query:
                code_dict['userid'] = i.user_id
                code_dict['expiry_at'] = i.expiry_at

            now = datetime.datetime.now()
            now_in_unix = time.mktime(now.timetuple())

            if float(now_in_unix) > float(code_dict['expiry_at']):
                return None, f'the expiry code, {unique_relative_code} has expired'

            new_familyid = str(uuid.uuid4())
            logged_in_mappingid = str(uuid.uuid4())
            logged_in_userid = userid

            resp_family_logged_in = DBFamilyUserMapping(mappingid=logged_in_mappingid,
                                                        userid=logged_in_userid,
                                                        familyid=new_familyid,
                                                        date_added=now)
            incoming_mappingid = str(uuid.uuid4())
            incoming_userid = str(code_dict['userid'])

            if logged_in_userid == incoming_userid:
                return None, f'You cannot add yourself to a family'

            resp_family_incoming = DBFamilyUserMapping(mappingid=incoming_mappingid,
                                                       userid=incoming_userid,
                                                       familyid=new_familyid,
                                                       date_added=now)
            new_family_key = str(uuid.uuid4())
            resp_common_family = DBFamilies(family_key=new_family_key,
                                            familyid=new_familyid,
                                            created_at=now,
                                            created_by=logged_in_userid)

            # now to add relations in DBRelations table

            relation_type_list = ['Parent', 'Spouse', 'Child']
            if relation_type not in relation_type_list:
                return None, f'Enter valid relation type'

            incoming_to_logged_in_user_relation = relation_type

            if incoming_to_logged_in_user_relation == 'Parent':
                logged_in_to_incoming_user_relation = 'Child'

            if incoming_to_logged_in_user_relation == 'Spouse':
                logged_in_to_incoming_user_relation = 'Spouse'

            if incoming_to_logged_in_user_relation == 'Child':
                logged_in_to_incoming_user_relation = 'Parent'

            relation_logged_in_key = str(uuid.uuid4())
            relation_incoming_key = str(uuid.uuid4())
            resp_relation_logged_in_user = DBRelations(relation_key=relation_logged_in_key,
                                                       relative_A=logged_in_userid,
                                                       relative_B=incoming_userid,
                                                       relation_type=logged_in_to_incoming_user_relation,
                                                       date_added=str(now))

            resp_relation_incoming_user = DBRelations(relation_key=relation_incoming_key,
                                                      relative_A=incoming_userid,
                                                      relative_B=logged_in_userid,
                                                      relation_type=incoming_to_logged_in_user_relation,
                                                      date_added=str(now))

            try:
                self.db.add(resp_family_logged_in)
                self.db.add(resp_family_incoming)
                self.db.add(resp_common_family)
                self.db.add(resp_relation_logged_in_user)
                self.db.add(resp_relation_incoming_user)

                self.db.commit()

            except sqlalchemy.exc.SQLAlchemyError as d:
                self.db.rollback()
                err = str(d)
                return None, '', f'DB Error code {err} for creating new family'

            except BaseException as e:
                self.db.rollback()
                err = str(e)
                return None, '', f'Internal error code {err} for creating new family'

            final_resp = CreateNewFamilyResponse(family_key=new_family_key,
                                                 familyid=new_familyid,
                                                 created_at=str(now),
                                                 created_by=logged_in_userid)

            return final_resp, f'Family created sucessfully by {logged_in_userid}, and {incoming_userid}\
                            added to the family.'

        except Exception as e:
            return None, str(e)

    def family_member_add_notif_data(self, request_data):
        try:
            # logger.info(request_data)
            frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)

            member_one = self.db.query(dbmodels.DBUser).filter_by(
                userid=request_data['member_one_id']).one_or_none()
            member_one_id = request_data['member_one_id']
            member_one_name = member_one.firstname + ' ' + member_one.lastname

            member_two_data = self.db.query(dbmodels.DBFamilyUserMapping).filter(
                dbmodels.DBFamilyUserMapping.userid != request_data['member_one_id'],
                dbmodels.DBFamilyUserMapping.familyid == request_data['family_id'],
                dbmodels.DBFamilyUserMapping.date_added == request_data['added_on']).first()
            # logger.info(member_two_data.userid)

            member_two_id = member_two_data.userid
            member_two = self.db.query(dbmodels.DBUser).filter_by(userid=member_two_id).one_or_none()

            member_two_name = member_two.firstname + ' ' + member_two.lastname
            # logger.info(member_one_name, member_two_name)

            relation_a_to_b = self.db.query(dbmodels.DBRelations).filter_by(relative_A=member_two_id,
                                                                            relative_B=member_one_id).one_or_none().relation_type
            if relation_a_to_b == 'Child':  # a is child of b
                relation_b_to_a = 'Parent'  # b is parent of a
            elif relation_a_to_b == 'Parent':
                relation_b_to_a = 'Child'
            else:
                relation_b_to_a = 'Spouse'
            # logger.info(relation_a_to_b)
            # logger.info(relation_b_to_a)

            # family_member_add = FamilyMemberAddEvent(family_id=request_data['family_id'],
            #                                          # mapping_id:str,
            #                                          relative_one_id=member_one_id,
            #                                          relative_one_name=member_one_name,
            #                                          relative_one_relation_with_relative_two=relation_a_to_b,
            #                                          relative_two_id=member_two_id,
            #                                          relative_two_name=member_two_name,
            #                                          is_member=True,
            #                                          relative_two_relation_with_relative_one=relation_b_to_a,
            #                                          event_date=request_data['added_on'],  # add or remove date
            #                                          remarks='None')
            # frbs_ctrl.family_member_add(family_member_event=family_member_add)

            return True

        except Exception as e:
            return None, str(e)

    def family_member_remove_notif_data(self, request_data):
        try:
            # logger.info(request_data)
            frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)

            member_one = self.db.query(dbmodels.DBUser).filter_by(
                userid=request_data['member_one_id']).one_or_none()

            member_one_name = member_one.firstname + ' ' + member_one.lastname

            member_two = self.db.query(dbmodels.DBUser).filter_by(
                userid=request_data['member_two_id']).one_or_none()

            member_two_name = member_two.firstname + ' ' + member_two.lastname
            # logger.info(member_one_name, member_two_name)
            # family_member_remove = FamilyMemberAddEvent(family_id=request_data['familyid'],
            #                                             # mapping_id:str,
            #                                             relative_one_id=request_data['member_one_id'],
            #                                             relative_one_name=member_one_name,
            #                                             relative_one_relation_with_relative_two=request_data[
            #                                                 'relation_a_to_b'],
            #                                             relative_two_id=request_data['member_two_id'],
            #                                             relative_two_name=member_two_name,
            #                                             is_member=False,
            #                                             relative_two_relation_with_relative_one=request_data[
            #                                                 'relation_b_to_a'],
            #                                             event_date=request_data['event_date'],  # add or remove date
            #                                             remarks='None')
            # # logger.info(family_member_remove)
            # frbs_ctrl.family_member_remove(family_member_event=family_member_remove)

            return True

        except Exception as e:
            return None, str(e)

    def add_new_member_to_family(self, request_data: AddRelativeToFamilyView, userid: str):

        try:

            family_query_resp: DBFamilies = self.db.query(DBFamilies).filter_by(created_by=userid).one_or_none()

            if family_query_resp:
                resp, msg = self.add_relative_to_family(request_data=request_data, userid=userid)
                if resp is None:
                    return None, msg
                added_on = dict(resp)['added_on']

            if not family_query_resp:
                resp, msg = self.create_new_family(request_data=request_data, userid=userid)
                if resp is None:
                    return None, msg
                added_on = dict(resp)['created_at']

            if not resp:
                return None, str(msg)

            if resp:
                new_resp = dict(
                    member_one_id=userid,
                    family_id=dict(resp)['familyid'],
                    added_on=added_on
                )
                self.family_member_add_notif_data(request_data=new_resp)

            return resp, f'member added successfully'

        except Exception as e:
            return None, str(e)

    def give_data_access(self, request_data: DataAccessView, userid: str):

        try:
            logged_in_userid = userid
            access_to_userid = request_data.access_to
            access_period = request_data.months_of_access
            access_period_list = [1, 6]
            if access_period not in access_period_list:
                return None, f'Please enter valid value of months_of_access, either 1 or 6'

            if self.check_if_family(relative_a=logged_in_userid, relative_b=access_to_userid) is False:
                return None, f'This user is not in your Family, please try again after adding them to your family'

            start_date = datetime.datetime.now()

            expiry_at = datetime.datetime.now() + relativedelta(months=+access_period)
            expiry_at_unix = time.mktime(expiry_at.timetuple())

            new_access_key = str(uuid.uuid4())

            data_access_resp = DBDataAccess(access_key=new_access_key,
                                            access_from=logged_in_userid,
                                            access_to=access_to_userid,
                                            start_date=str(start_date),
                                            expiry_at=expiry_at_unix)

            try:
                self.db.add(data_access_resp)
                self.db.commit()

            except sqlalchemy.exc.SQLAlchemyError as d:
                self.db.rollback()
                err = str(d)
                return None, '', f'DB Error code {err} for providing data access'

            except BaseException as e:
                self.db.rollback()
                err = str(e)
                return None, '', f'Internal error code {err} for providing data access'

            expiry_in_str = str(access_period) + " " + 'months'

            final_resp = DataAccessResponse(access_key=new_access_key,
                                            access_from=logged_in_userid,
                                            access_to=access_to_userid,
                                            expiry_in=expiry_in_str)

            return final_resp, f'Access provided sucessfully'

        except Exception as e:
            return None, str(e)

    def check_if_access(self, access_from: str, access_to: str):

        access_dict = {}
        now = datetime.datetime.now()
        now_in_unix = time.mktime(now.timetuple())

        access_query = self.db.query(DBDataAccess).filter_by(access_from=str(access_from)).filter_by(
            access_to=str(access_to)).all()

        for i in access_query:
            access_dict['access_key'] = i.access_key
            access_dict['access_from'] = i.access_from
            access_dict['access_to'] = i.access_to
            access_dict['start_date'] = i.start_date
            access_dict['expiry_at'] = i.expiry_at

        if not access_dict:
            resp_bool = 'False'

        if access_dict:
            if now_in_unix < access_dict['expiry_at']:
                resp_bool = 'True'

            if now_in_unix > access_dict['expiry_at']:
                resp_bool = 'False'

        return resp_bool, f'resp bool'

    def check_if_family(self, relative_a: str, relative_b: str):

        family_query_a = self.db.query(DBFamilyUserMapping).filter_by(userid=str(relative_a)).all()
        family_query_b = self.db.query(DBFamilyUserMapping).filter_by(userid=str(relative_b)).all()

        for i in family_query_a:
            family_id_a = str(i.familyid)

        for j in family_query_b:
            family_id_b = str(j.familyid)

        if family_id_a == family_id_b:
            resp_bool = True

        else:
            resp_bool = False

        return resp_bool, f'bool from check_if_family'

    def get_user_families(self, userid: str):

        primary_family_dict = {}
        final_resp = {}
        relatives = []

        primary_family_query = self.db.query(DBFamilies).filter_by(created_by=str(userid)).one_or_none()

        if not primary_family_query:
            final_resp['Primary_Family'] = []
            primary_familyid = ''

        if primary_family_query:

            primary_family_dict['family_key'] = primary_family_query.familiy_key
            primary_family_dict['familyid'] = primary_family_query.familyid
            primary_family_dict['created_at'] = primary_family_query.created_at
            primary_family_dict['created_by'] = primary_family_query.created_by

            primary_familyid = str(primary_family_dict['familyid'])

            primary_family_members_list, msg = self.get_members_from_familyid(familyid=primary_familyid)

            relation = None

            for relativeid in primary_family_members_list:
                if userid != relativeid:
                    relation = self.get_relation_from_userids(relative_a=relativeid, relative_b=userid)
                if userid == relativeid:
                    relation = 'Self'
                relative_details = self.__get_patient_details(patientid=relativeid)
                relative_details['relation'] = relation
                relative_details['userid'] = relativeid
                relatives.append(copy.deepcopy(relative_details))

        # # secondary family
        secondary_family_query = self.db.query(DBFamilyUserMapping).filter_by(userid=userid).all()

        secondary_familyid_list = []
        secondary_family_userids = []

        for k in secondary_family_query:
            s_familyid = k.familyid
            if s_familyid != primary_familyid:
                secondary_familyid_list.append(s_familyid)
            # a list of secondary familyids only (excluding primary familyids)

        for secondary__familyid in secondary_familyid_list:
            secondary_relatives_list, msg = self.get_members_from_familyid(familyid=secondary__familyid)
            secondary_family_userids.append((secondary_relatives_list, secondary__familyid))

        secondary_user_temp_list = []
        secondary_family_temp_list = []
        for secondary_family_key in secondary_family_userids:
            for secondary_userid_key in secondary_family_key[0]:
                secondary_relative_details = self.__get_patient_details(patientid=secondary_userid_key)
                relative_a = self.family_creator_from_familyid(secondary_family_key[1])
                relative_b = secondary_userid_key
                secondary_relation = self.get_relation_from_userids(relative_a=relative_b, relative_b=relative_a)
                secondary_relative_details['Relation'] = secondary_relation
                secondary_relative_details['userid'] = secondary_userid_key
                secondary_user_temp_list.append(secondary_relative_details)
            secondary_family_temp_list.append(copy.deepcopy(secondary_user_temp_list))

        book_appt_family_query = self.db.query(DBRelatives).filter_by(caretaker_id=userid)

        book_appt_relative_dict = {}
        book_appt_relative_list = []

        for book_appt_relative in book_appt_family_query:
            book_appt_relative_dict['relativeid'] = book_appt_relative.relativeid
            book_appt_relative_dict['caretaker_id'] = book_appt_relative.caretaker_id
            book_appt_relative_dict['relationship'] = book_appt_relative.relationship
            book_appt_relative_dict['firstname'] = book_appt_relative.firstname
            book_appt_relative_dict['lastname'] = book_appt_relative.lastname
            book_appt_relative_dict['birthdate'] = book_appt_relative.birthdate
            book_appt_relative_dict['email'] = book_appt_relative.email
            book_appt_relative_dict['mobile'] = book_appt_relative.mobile
            book_appt_relative_dict['gender'] = book_appt_relative.gender
            book_appt_relative_list.append(copy.deepcopy(book_appt_relative_dict))

        final_resp['Primary_Family'] = relatives
        final_resp['Secondary_Family'] = secondary_family_temp_list
        final_resp['Family_book_appointment'] = book_appt_relative_list

        return final_resp, f'All families resp'

    def get_members_from_familyid(self, familyid: str):

        family_members_primary_family_query = self.db.query(DBFamilyUserMapping).filter_by(familyid=familyid).all()

        primary_family_members_dict = {}
        primary_family_members_list1 = []
        primary_family_members_list = []

        for j in family_members_primary_family_query:
            primary_family_members_dict['mappingid'] = j.mappingid
            primary_family_members_dict['userid'] = j.userid
            primary_family_members_dict['familyid'] = j.familyid
            primary_family_members_dict['date_added'] = j.date_added
            member_userid = j.userid
            primary_family_members_list1.append(copy.deepcopy(primary_family_members_dict))
            primary_family_members_list.append(copy.deepcopy(member_userid))

        if not primary_family_members_dict:
            return None, f'no Family with this familyid'

        return primary_family_members_list, f'family members found sucessfully'

    def get_relation_from_userids(self, relative_a: str, relative_b: str):

        relation_query = self.db.query(DBRelations).filter_by(relative_A=relative_a, relative_B=relative_b).all()
        final_relation = None

        for i in relation_query:
            final_relation = i.relation_type

        if relative_a == relative_b:
            final_relation = 'Self'

        return final_relation

    def family_creator_from_familyid(self, familyid: str):

        family_creator_query = self.db.query(DBFamilies).filter_by(familyid=familyid).all()

        for i in family_creator_query:
            created_by = i.created_by

        return created_by

    def add_relations(self, logged_in_userid: str, incoming_userid: str, relation_type: str):

        now = datetime.datetime.now()
        relation_type_list = ['Parent', 'Spouse', 'Child']

        if relation_type not in relation_type_list:
            return None, f'Enter valid relation type'

        incoming_to_logged_in_user_relation = relation_type

        if incoming_to_logged_in_user_relation == 'Parent':
            logged_in_to_incoming_user_relation = 'Child'

        if incoming_to_logged_in_user_relation == 'Spouse':
            logged_in_to_incoming_user_relation = 'Spouse'

        if incoming_to_logged_in_user_relation == 'Child':
            logged_in_to_incoming_user_relation = 'Parent'

        relation_logged_in_key = str(uuid.uuid4())
        relation_incoming_key = str(uuid.uuid4())

        resp_relation_logged_in_user = DBRelations(relation_key=relation_logged_in_key,
                                                   relative_A=logged_in_userid,
                                                   relative_B=incoming_userid,
                                                   relation_type=logged_in_to_incoming_user_relation,
                                                   date_added=str(now))

        resp_relation_incoming_user = DBRelations(relation_key=relation_incoming_key,
                                                  relative_A=incoming_userid,
                                                  relative_B=logged_in_userid,
                                                  relation_type=incoming_to_logged_in_user_relation,
                                                  date_added=str(now))
        try:
            self.db.add(resp_relation_logged_in_user)
            self.db.add(resp_relation_incoming_user)

            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, '', f'DB Error code {err} for adding new relations'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, '', f'Internal error code {err} for addinf new relations'

        logged_in_relation_resp = AddRelationResp(relation_key=relation_logged_in_key,
                                                  relative_a=logged_in_userid,
                                                  relative_b=incoming_userid,
                                                  relation_type=logged_in_to_incoming_user_relation)

        incoming_relation_resp = AddRelationResp(relation_key=relation_incoming_key,
                                                 relative_a=incoming_userid,
                                                 relative_b=logged_in_userid,
                                                 relation_type=incoming_to_logged_in_user_relation)

        final_resp = (logged_in_relation_resp, incoming_relation_resp)

        return final_resp, f'relation added successfully'

    def admin_add_new_member_to_family(self, request_data: AdminAddRelativeToFamilyView):
        try:
            userid = request_data.userid

            family_query_resp: DBFamilies = self.db.query(DBFamilies).filter_by(created_by=userid).one_or_none()

            if family_query_resp:
                resp, msg = self.add_relative_to_family(request_data=request_data, userid=userid)
                added_on = dict(resp)['added_on']
            if not family_query_resp:
                resp, msg = self.create_new_family(request_data=request_data, userid=userid)
                added_on = dict(resp)['created_at']

            if not resp:
                return None, str(msg)
            if resp:
                new_resp = dict(
                    member_one_id=userid,
                    family_id=dict(resp)['familyid'],
                    added_on=added_on
                )
                self.family_member_add_notif_data(request_data=new_resp)
            return resp, f'member added successfully'

        except Exception as e:
            return None, str(e)

    def admin_get_all_families(self):
        try:
            familyid_list = []
            final_resp = []
            all_family_query = self.db.query(DBFamilies).all()
            for i in all_family_query:
                familyid_list.append(i.familyid)

            for j in familyid_list:
                members, msg = self.get_members_from_familyid(j)
                final_resp.append({j: members})

            return final_resp, f'all families returned sucessfully'

        except Exception as e:
            return None, str(e)

    def admin_get_user_families(self, request_data: AdminGetUserFamilyRequest):
        try:
            userid = request_data.userid
            if not userid:
                return None, f'please enter valid userid'
            resp, msg = self.get_user_families(userid=userid)
            return resp, f'Families returned sucessfully'
        except Exception as e:
            return None, str(e)

    def remove_member_from_family(self, request_data: RemoveFamilyMemberRequest, userid: str):
        try:
            relative_id = request_data.relative_userid
            if not relative_id:
                return None, f'please proveide valid relativeid'

            if relative_id == userid:
                return None, f'relative and user cannnot have same id'

            if self.check_if_family(relative_a=userid, relative_b=relative_id) is False:
                return None, f'This user is not in your Family, please try again after adding them to your family'

            familyid_query = self.db.query(DBFamilies).filter_by(created_by=userid).one_or_none()

            familyid = familyid_query.familyid

            family_user_mapping_query = self.db.query(DBFamilyUserMapping).filter_by(userid=relative_id).filter_by(
                familyid=familyid).one_or_none()

            user_relative_relation_query = self.db.query(DBRelations).filter_by(relative_A=userid).filter_by(
                relative_B=relative_id).one_or_none()

            relation_a_to_b = user_relative_relation_query.relation_type

            relative_user_relation_query = self.db.query(DBRelations).filter_by(relative_A=relative_id).filter_by(
                relative_B=userid).one_or_none()

            relation_b_to_a = relative_user_relation_query.relation_type

            try:
                self.db.delete(family_user_mapping_query)
                self.db.delete(user_relative_relation_query)
                self.db.delete(relative_user_relation_query)

                self.db.commit()
            except sqlalchemy.exc.SQLAlchemyError as d:
                self.db.rollback()
                err = str(d)
                return None, f'DB Error code {err} for deleting relative {relative_id}'
            except BaseException as e:
                self.db.rollback()
                err = str(e)
                return None, f'Internal Error code {err} for {relative_id}'

            final_resp = {}

            status = f'Relative {relative_id} removed from {userid}\'s family sucessfully'
            final_resp['mapping_id'] = family_user_mapping_query.mappingid
            final_resp['relative_id'] = family_user_mapping_query.userid
            final_resp['familyid'] = family_user_mapping_query.familyid
            final_resp['date_added'] = family_user_mapping_query.date_added
            final_resp['status'] = status

            notif_resp = dict(familyid=family_user_mapping_query.familyid,
                              member_one_id=userid,
                              member_two_id=relative_id,
                              event_date=datetime.datetime.now(),
                              relation_a_to_b=relation_a_to_b,
                              relation_b_to_a=relation_b_to_a)
            # logger.info(notif_resp)
            self.family_member_remove_notif_data(notif_resp)

            return final_resp, f'family member removed'

        except Exception as e:
            return None, str(e)

    def admin_remove_member_from_family(self, request_data: AdminRemoveFamilyMemberRequest):
        try:
            userid = request_data.userid
            relative_userid = request_data.relative_userid
            if not userid:
                return None, f'please enter valid userid'
            if not relative_userid:
                return None, f'please enter valid relative userid'

            remove_member_input = RemoveFamilyMemberRequest(relative_userid=relative_userid)

            remove_member_resp, msg = self.remove_member_from_family(request_data=remove_member_input, userid=userid)

            return remove_member_resp, f'relative {relative_userid} removed sucessfully from {userid}\'s family '

        except Exception as e:
            return None, str(e)

    def add_family_doctor(self, doctor_id: str, user_id: str):
        try:
            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            user_ctrl = UserController(db=self.db, otp_generator=None)
            user_name = ''
            user_details = user_ctrl.get_user_by_id(user_id)
            if user_details:
                user_name = user_details.firstname + ' ' + user_details.lastname
            else:
                return None, 'Invalid user id'
            doctor_name = ''
            doctor_details, msg = doctor_ctrl.get_by_id(
                doctorid=doctor_id)
            if doctor_details is not None:
                doctor_name = doctor_details['firstname'] + ' ' + doctor_details['lastname']
            else:
                return None, 'Invalid doctor id'

            family_doctor_active_status, msg = doctor_ctrl.get_family_doctor_status(doctor_id=doctor_id)
            logger.info(family_doctor_active_status['family_doctor_active'])
            if not family_doctor_active_status['family_doctor_active']:
                return None, f'Doctor with id {doctor_id} is not active for being Family Doctor'

            membership_plan_active = self.mongo_db['ACareSubscription'].find_one(
                {"userid": user_id, "valid_till": {"$gt": datetime.datetime.now()}})
            if not membership_plan_active:
                return None, 'User is not a AyooCare subscribed member'
            user_exists = self.mongo_db['UserCollection'].find_one(dict(userid=user_id))
            logger.info(user_exists)
            if user_exists is not None:
                self.mongo_db['UserCollection'].find_one_and_update({"userid": user_id},
                                                                    {"$set": {"family_doctor": doctor_id}})
            else:
                self.mongo_db['UserCollection'].insert_one({"userid": user_id, "family_doctor": doctor_id})

            frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)
            family_doctor_notif = FamilyDoctorEvent(
                user_id=user_id,
                user_name=user_name,
                doctor_id=doctor_id,
                doctor_name=doctor_name,
                event_date=datetime.datetime.now(),
                event_type="Added",
                remarks="None")
            frbs_ctrl.family_doctor_add(family_doctor_event=family_doctor_notif)

            return "Family doctor added", "success"
        except Exception as e:
            return None, f'error occurred as {str(e)} while adding family doctor'

    def remove_family_doctor(self, doctor_id: str, user_id: str):
        try:
            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            user_ctrl = UserController(db=self.db, otp_generator=None)
            user_name = ''
            user_details = user_ctrl.get_user_by_id(user_id)
            if user_details:
                user_name = user_details.firstname + ' ' + user_details.lastname
            else:
                return None, 'Invalid user id'
            doctor_name = ''
            doctor_details, msg = doctor_ctrl.get_by_id(
                doctorid=doctor_id)
            if doctor_details is not None:
                doctor_name = doctor_details['firstname'] + ' ' + doctor_details['lastname']
            else:
                return None, 'Invalid doctor id'

            family_doctor_exists = self.mongo_db['UserCollection'].find_one(
                dict(userid=user_id, family_doctor=doctor_id))
            logger.info(family_doctor_exists)
            if family_doctor_exists is not None:
                self.mongo_db['UserCollection'].find_one_and_update({"userid": user_id},
                                                                    {"$unset": {"family_doctor": ""}})
            else:
                return None, "Family doctor not found for the user"
            frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)
            family_doctor_notif = FamilyDoctorEvent(
                user_id=user_id,
                user_name=user_name,
                doctor_id=doctor_id,
                doctor_name=doctor_name,
                event_date=datetime.datetime.now(),
                event_type="Removed",
                remarks="None")
            frbs_ctrl.family_doctor_remove(family_doctor_event=family_doctor_notif)
            return "Family doctor removed", "success"
        except Exception as e:
            return None, f'error occurred as {str(e)} while removing family doctor'

    def get_users_family_doctor(self, user_id: str):
        try:
            user_ctrl = UserController(db=self.db, otp_generator=None)
            user_details = user_ctrl.get_user_by_id(user_id)

            if user_details is None:
                return None, 'Invalid user id'

            user_exists = self.mongo_db['UserCollection'].find_one(dict(userid=user_id))
            logger.info(user_exists)
            # logger.info(user_exists['family_doctor'])
            if (user_exists is None) or (not 'family_doctor' in user_exists):
                logger.info('false')
                return None, 'User is not having any family doctor'

            doctor_details: DBDoctor = self.db.query(dbmodels.DBDoctor).filter(
                DBDoctor.doctorid == user_exists['family_doctor']).one_or_none()
            # logger.info(doctor_details)
            family_doctor = {
                "family_doctor": user_exists['family_doctor'],
                "doctor_details": doctor_details
            }
            return dict(family_doctor), "Family doctor found"

        except Exception as e:
            return None, f'error occurred as {str(e)} while getting family doctor of user'

    def get_doctors_family_doctor_details(self, doctor_id: str):
        try:
            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)

            doctor_details, msg = doctor_ctrl.get_by_id(
                doctorid=doctor_id)
            if doctor_details is None:
                return None, 'Invalid doctor id'

            family_doctor = self.mongo_db['UserCollection'].find(dict(family_doctor=doctor_id))
            users = []
            all_users = list(family_doctor.clone())
            # logger.info(len(all_users))
            if len(all_users) == 0:
                return None, 'Doctor is not a family doctor for any user'

            for user in all_users:
                user_info: DBUser = self.db.query(dbmodels.DBUser).filter(
                    DBUser.userid == user['userid']).one_or_none()
                users.append(user_info)

            return {"Users": users}, "Users with family doctor of given id found"
        except Exception as e:
            return None, f'error occurred as {str(e)} while adding family doctor'

    def check_perms(self, operation: str, role: str):
        from ayoo_backend.api import dbmodels
        resp_data = {}
        resp_data: DBRBAC = self.db.query(dbmodels.DBRBAC).filter_by(operation=operation).one_or_none()
        if resp_data:
            if role == 'admin':
                resp = resp_data.admin
            elif role == 'patient':
                resp = resp_data.patient
            elif role == 'nurse':
                resp = resp_data.nurse
            elif role == 'doctor':
                resp = resp_data.doctor
            elif role == 'lab_assistant':
                resp = resp_data.lab_assistant
            elif role == 'transcriptionist':
                resp = resp_data.transcriptionist
            elif role == 'clinic_admin':
                resp = resp_data.clinic_adimn
            elif role == 'allied_health_prof':
                resp = resp_data.allied_health_prof
            else:
                return None
        if resp == 1:
            return True
        else:
            return False

    def book_appointment_by_doctor(self, booking_data: DoctorAppointmentBooking, doctor_id: str):
        try:
            logger.info(doctor_id)
            valid_user: DBUser = self.db.query(DBUser).filter_by(userid=str(booking_data.patient_id)).one_or_none()
            if valid_user is None:
                return None, 'Invalid patient id'

            if booking_data.appointment_type == 'InClinic':
                valid_clinic: DBClinic = self.db.query(DBClinic).filter_by(
                    clinicid=str(booking_data.clinicid)).one_or_none()
                if valid_clinic is None:
                    return None, 'Invalid clinic id'

                doctor_res_duration: DBDoctor = self.db.query(DBDoctor).filter_by(
                    doctorid=str(doctor_id)).one_or_none()
                duration = doctor_res_duration.consulting_duration_clinic
                # Handle 85-minute consultation time to actually book a 90-minute slot
                if duration == 85:
                    duration = 90

                mapping_res = self.db.query(DBClinicAndDoctors).filter_by(doctorid=str(doctor_id)).all()
                if len(mapping_res):
                    for map_data in mapping_res:
                        if map_data.clinicid == booking_data.clinicid:
                            map_slots = self.mongo_db['DoctorAndClinic'].find_one(dict(mappingid=map_data.mappingid))

                            appointment_slot = datetime.datetime.strptime(booking_data.appointment_slot,
                                                                          "%Y-%m-%d %I:%M %p")
                            appointment_time = appointment_slot.time()
                            appointment_date = appointment_slot.date()

                            search_time = appointment_time.strftime("%I:%M %p")
                            search_date = appointment_date.strftime("%Y-%m-%d")

                            for slot in map_slots:

                                day_today = datetime.datetime.strptime(search_date, "%Y-%m-%d").weekday()
                                day_today = calendar.day_name[day_today]
                                if slot.lower() == day_today.lower():
                                    if len(map_slots[slot]):
                                        slot_data = map_slots[slot]

                                        for data in slot_data:
                                            avl_slots_check = self.available_slot(doctorid=doctor_id,
                                                                                  start_time=data['starts_at'],
                                                                                  end_time=data['ends_at'],
                                                                                  duration=duration,
                                                                                  search_date=search_date,
                                                                                  slot_id=data['slotid'])
                                            if search_time in avl_slots_check:

                                                appointment_object = AppointmentBooking(
                                                    appointment_type=booking_data.appointment_type,
                                                    appointment_for='Self',
                                                    symptoms=booking_data.symptoms,
                                                    additional_notes=booking_data.additional_notes,
                                                    # patientid=booking_data.patient_id,
                                                    clinicid=booking_data.clinicid,
                                                    doctorid=doctor_id,
                                                    appointment_slot=booking_data.appointment_slot,
                                                    payment='None')
                                                response_data, msg = self.book_appointment(
                                                    booking_data=appointment_object,
                                                    userid=doctor_id, booked_by_name='Doctor')
                                                if response_data is None:
                                                    return None, msg
                                                else:
                                                    return response_data, msg
                                            else:
                                                return None, 'Invalid slot timings'
                else:
                    return None, 'No mapped clinics found'

            else:
                appointment_object = AppointmentBooking(
                    appointment_type=booking_data.appointment_type,
                    appointment_for='Self',
                    symptoms=booking_data.symptoms,
                    additional_notes=booking_data.additional_notes,
                    # patientid=booking_data.patient_id,
                    clinicid=booking_data.clinicid,
                    doctorid=doctor_id,
                    appointment_slot=booking_data.appointment_slot,
                    payment='None')
                response_data, msg = self.book_appointment(booking_data=appointment_object,
                                                           userid=booking_data.patient_id,
                                                           booked_by=doctor_id)
                if response_data is not None:
                    return response_data, msg
                else:
                    return None, msg
        except Exception as e:
            return None, f'error occurred as {str(e)} while booking appointment'

    def med_notif_toggle_switch(self, caseid: str, userid: str):
        try:
            raw_data = self.mongo_db['UserCollection'].find_one(
                {"userid": userid, "cases.caseid": caseid},
                {"cases": {"caseid": 1, "isOpen": 1, "notif_toggle": 1}})
            if raw_data:
                data = raw_data['cases']
                for case in data:
                    if case['caseid'] == caseid and case['isOpen'] == 'True':
                        notif_toggle = case['notif_toggle']

                        if notif_toggle == True or notif_toggle == 'True':
                            toggle = False
                        else:
                            toggle = True

                        self.mongo_db['UserCollection'].find_one_and_update(
                            {"userid": userid, "cases.caseid": caseid},
                            {'$set': {"cases.$.notif_toggle": toggle}}, {'new': True})

                        return {"status": toggle}, 'Success'
                return None, f'No open case with id: {caseid} found for the user'

            else:
                return None, f'No case with id: {caseid} found for the user'
        except Exception as e:
            return None, f'error occurred as {str(e)} while switching medicine notifications'

    def get_appointment_details_for_user(self, appointment_id: str, logged_in_user: str):
        try:

            appointment_data = self.mongo_db['Appointments'].find_one(dict(appointment_id=appointment_id))
            if appointment_data is None:
                raise Exception(f'Invalid appointment ID: {appointment_id}')

            if appointment_data['patient_id'] != logged_in_user:
                raise Exception(f'Invalid User ID')

            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            # user_ctrl = UserController(db=self.db, otp_generator=None)

            clinic = {}
            if appointment_data['clinicid'] is not None and appointment_data['clinicid'] != '':
                clinic_details = doctor_ctrl.get_clinic_by_id(clinicid=appointment_data['clinicid'])
                if clinic_details:
                    # clinic = clinic_details.name
                    clinic = dict(
                        clinic_id=clinic_details.clinicid,
                        clinic_name=clinic_details.name,
                        clince_mobile=clinic_details.mobile,
                        clinic_starts_at=clinic_details.starts_at,
                        clinic_ends_at=clinic_details.ends_at,
                        clinic_address=clinic_details.address,
                        lat=clinic_details.lat,
                        lon=clinic_details.lon
                    )
            get_doctor_details, msg = doctor_ctrl.get_by_id(
                doctorid=appointment_data['doctorid'])
            doctor_data = {}
            if get_doctor_details is not None:
                doctor_data = {
                    'name': get_doctor_details['firstname'] + ' ' + get_doctor_details['lastname'],
                    'languages': get_doctor_details['languages'],
                    'graduation': get_doctor_details['graduation'],
                    'masters': get_doctor_details['masters'],
                    'doctortype': get_doctor_details['doctortype'],
                    'specialization': get_doctor_details['specialization'],
                    'specialization_field': get_doctor_details['specialization_field'],
                    'additional_qualification': get_doctor_details['additional_qualification'],
                    'bio': get_doctor_details['bio'],
                    'consulting_duration_virtual': get_doctor_details['consulting_duration_virtual'],
                    'consulting_duration_clinic': get_doctor_details['consulting_duration_clinic'],
                    'consulting_fees_virtual': get_doctor_details['consulting_fees_virtual'],
                    'consulting_fees_clinic': get_doctor_details['consulting_fees_clinic'],
                    'image_id': get_doctor_details['image_id'],
                    'profile_image_url': get_doctor_details['profile_image_url']
                }
            patient_meeting_link_info = None
            patient_meeting_code = None
            if appointment_data['appointment_type'] == 'Virtual':
                jitsi_ctrl = JitsiMeetController(db=self.db, mongo=self.mongo)
                meeting_info = jitsi_ctrl.check_meeting_using_appointment_id(
                    appointment_id=str(appointment_data['appointment_id']))
                patient_meeting_link_info = meeting_info[
                    'patient_joining_link'] if 'patient_joining_link' in meeting_info else None
                patient_meeting_code = meeting_info['meeting_code'] if 'meeting_code' in meeting_info else None

            patient = self.__get_patient_details(patientid=appointment_data['patient_id'])
            # patient['dob'] = str(patient['dob'])

            data_to_return = NotificationPushMessage(
                appointment_id=appointment_data['appointment_id'],
                caseid=appointment_data['caseid'],
                symptoms=appointment_data['symptoms'],
                symptoms_audio_clip=appointment_data['symptoms_audio_clip'],
                additional_notes=appointment_data['additional_notes'],
                appointment_slot=appointment_data['appointment_slot'].strftime("%Y-%m-%dT%H:%M:%S"),
                appointment_type=appointment_data['appointment_type'],
                appointment_for=appointment_data['appointment_for'],
                patient=patient,
                clinic=clinic,
                doctor=doctor_data,
                meeting_link=patient_meeting_link_info,
                meeting_code=patient_meeting_code
            )

            return data_to_return

        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error while fetching appointment details: {str(e)}')

    def get_app_download_link(self, user_data: AppDownloadLink):
        try:
            user_request_exist = self.mongo_db['AppDownloadRequests'].find_one(
                dict(mobile_number=user_data.mobile_number))

            uid = str(uuid.uuid4())
            if user_request_exist is None:
                self.mongo_db['AppDownloadRequests'].insert_one(dict(
                    u_id=uid,
                    mobile_number=user_data.mobile_number,
                    date_created=datetime.datetime.now(),
                    date_updated=datetime.datetime.now(),
                    link_sent_count=1
                ))
            else:
                self.mongo_db['AppDownloadRequests'].find_one_and_update(dict(
                    mobile_number=user_data.mobile_number), {
                    "$set": {
                        'date_updated': datetime.datetime.now(),
                        'link_sent_count': int(user_request_exist['link_sent_count']) + 1
                    }
                })

            text_local_controller = TextLocalController()
            msg_status, resp_msg = text_local_controller.send_sms(template_name='GetAyooApp', var_list=[''],
                                                                  numbers=user_data.mobile_number)

            message = "Download the AYOO App now! All your medical healthcare needs at your fingertips. Get personalized care, manage your family's physical and mental well-being remotely, and directly message your care provider. Experience a modern approach to healthcare. Get AYOO App. https://onelink.to/9mv5pp"

            aws_mail_ctrl = AWSEmailAndMsgSender()
            aws_mail_ctrl.get_ayoo_app(message=message,
                                       mobile=user_data.mobile_number,
                                       email=None)
            logger.info(f'Download link sent to: {user_data.mobile_number}')

            return dict(mobile_number=user_data.mobile_number, download_link_status=msg_status)
        except Exception as e:
            raise HTTPException(status_code=409,
                                detail=f'Error occurred while getting download link for the app: {str(e)}')

    def get_patient_relatives_list_from_admin_and_user(self, data: PatientsRelatives):
        try:
            if data.patient_id is None or data.patient_id == '':
                raise Exception('Patient ID is required')

            patient = self.__get_patient_details(patientid=data.patient_id)
            if patient is None:
                raise Exception(f'Invalid patient ID: {data.patient_id}')

            if data.relation:
                all_relatives = self.db.query(DBRelatives).filter(
                    DBRelatives.caretaker_id == data.patient_id,
                    DBRelatives.relationship == data.relation
                ).all()
            else:
                all_relatives = self.db.query(DBRelatives).filter(
                    DBRelatives.caretaker_id == data.patient_id
                ).all()
            relatives = []
            mongo_collection_appointments = self.mongo_db['Appointments']
            mongo_collection_docs = self.mongo_db['UserLocker']
            date_from = datetime.datetime.now()
            emergency_contacts = []
            user_details = self.mongo_db['UserCollection'].find_one({'userid': data.patient_id})
            if user_details is not None:
                emergency_contacts = user_details.get('emergency_contacts', [])

            for relative in all_relatives:
                notif_flag = False
                notification_catagories = []
                appointments_list = list(mongo_collection_appointments.find(
                    {
                        '$or': [
                            {'patient_id': relative.relativeid}
                        ],
                        '$and': [
                            {'appointment_slot': {'$gte': date_from}},
                            {'is_active': True},
                            {'is_confirmed': True}
                        ]
                    }
                )
                )
                if len(appointments_list) > 0:
                    notif_flag = True
                    notification_catagories.append("Appointment")

                doc_upload_requests = list(mongo_collection_docs.find(
                    {
                        "userid": relative.relativeid,
                        "isRequested": True
                    }
                ))

                if len(doc_upload_requests) > 0:
                    notif_flag = True
                    notification_catagories.append("Document Request")

                caretaker_data = self.db.query(DBRelatives).filter(
                    DBRelatives.caretaker_id == relative.relativeid,
                    DBRelatives.relativeid == data.patient_id
                ).one_or_none()
                if caretaker_data is not None:
                    consent_record = self.mongo_db['UserConsentRecord'].find_one(
                        {'relation_id': str(caretaker_data.relation_id)})
                    if consent_record is None:
                        relative_age = get_age_in_years(str(caretaker_data.birthdate))
                        if relative_age < 18:
                            turning_18_date = caretaker_data.birthdate.replace(year=caretaker_data.birthdate.year + 18)
                            consent_days_left = (turning_18_date - datetime.datetime.now().date()).days
                        else:
                            consent_days_left = 0
                    else:
                        consent_days = consent_record['consent_ends_on'] - datetime.datetime.now()
                        consent_days_left = consent_days.days + 1 if consent_days.days >= 0 else 0

                    is_emergency_contact = any(
                        contact.get('user_id', None) == str(relative.relativeid) for contact in emergency_contacts)

                    relatives.append(dict(
                        relativeid=str(relative.relativeid),
                        firstname=relative.firstname,
                        lastname=relative.lastname,
                        relationship=relative.relationship,
                        email=relative.email,
                        gender=relative.gender,
                        birthdate=relative.birthdate,
                        mobile=relative.mobile,
                        caretaker_id=relative.caretaker_id,
                        is_active=relative.is_active,
                        is_registered=relative.is_registered,
                        # consent=relative.consent if consent_days_left > 0 else False,
                        consent=relative.consent,
                        consent_days_left=consent_days_left,  # this is for reverse consent days left
                        reverse_consent=relative.reverse_consent if consent_days_left > 0 else False,
                        relation_id=relative.relation_id,
                        notification_flag=notif_flag,
                        notification_catagories=notification_catagories,
                        is_emergency_contact=is_emergency_contact
                    ))

            return dict(
                msg='Success',
                relatives=relatives
            )

        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error while fetching relatives details: {str(e)}')

    def get_patient_relatives_list_from_doctor(self, data: PatientsRelatives):
        try:
            if data.patient_id is None or data.patient_id == '':
                raise Exception('Patient ID is required')

            patient = self.__get_patient_details(patientid=data.patient_id)
            if patient is None:
                raise Exception(f'Invalid patient ID: {data.patient_id}')

            all_relatives = self.db.query(DBRelatives).filter(
                DBRelatives.caretaker_id == data.patient_id
            ).all()
            relatives = []
            emergency_contacts = []
            user_details = self.mongo_db['UserCollection'].find_one({'userid': data.patient_id})
            if user_details is not None:
                emergency_contacts = user_details.get('emergency_contacts', [])

            for relative in all_relatives:

                caretaker_data = self.db.query(DBRelatives).filter(
                    DBRelatives.caretaker_id == relative.relativeid,
                    DBRelatives.relativeid == data.patient_id
                ).one_or_none()
                if caretaker_data is not None:
                    consent_record = self.mongo_db['UserConsentRecord'].find_one(
                        {'relation_id': str(caretaker_data.relation_id)})
                    if consent_record is None:
                        relative_age = get_age_in_years(str(caretaker_data.birthdate))
                        if relative_age < 18:
                            turning_18_date = caretaker_data.birthdate.replace(year=caretaker_data.birthdate.year + 18)
                            consent_days_left = (turning_18_date - datetime.datetime.now().date()).days
                        else:
                            consent_days_left = 0
                    else:
                        consent_days = consent_record['consent_ends_on'] - datetime.datetime.now()
                        consent_days_left = consent_days.days + 1 if consent_days.days >= 0 else 0

                    is_emergency_contact = any(
                        contact.get('user_id', None) == str(relative.relativeid) for contact in emergency_contacts)

                    relatives.append(dict(
                        relativeid=str(relative.relativeid),
                        firstname=relative.firstname,
                        lastname=relative.lastname,
                        relationship=relative.relationship,
                        email=relative.email,
                        gender=relative.gender,
                        birthdate=relative.birthdate,
                        mobile=relative.mobile,
                        caretaker_id=relative.caretaker_id,
                        is_active=relative.is_active,
                        is_registered=relative.is_registered,
                        relation_id=relative.relation_id,
                        # consent=relative.consent if consent_days_left > 0 else False,
                        consent=relative.consent,
                        consent_days_left=consent_days_left,  # this is for reverse consent days left
                        reverse_consent=relative.reverse_consent if consent_days_left > 0 else False,
                        is_emergency_contact=is_emergency_contact
                    ))

            return relatives

        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error while fetching relatives details: {str(e)}')

    def cancel_appointment_from_retry(self, data: AppointmentCancellationModel, user_id):
        try:
            self.mongo_db['Appointments'].update_one(
                {
                    '$and': [
                        {'$or': [
                            {'patient_id': user_id},
                            {'booked_by': user_id}
                        ]}, {'appointment_id': data.appointment_id}
                    ]
                }, {'$set': {'status.status': "Abandoned",
                             "is_active": False,
                             "is_confirmed": False}})
            self.mongo_db['Paymentgateway3'].update_many(
                {'appointment_id': data.appointment_id, 'payment_status': 'initiate'},
                {'$set': {"payment_status": "userapp_abort"}}

            )
            return True, "Appointment Cancelled by User"
        except Exception as e:
            return None, "Appointment not Updated, Reason {}".format(e)

    def get_all_active_cases(self, userId):
        try:
            active_cases_dict = {}
            pipeline = [
                {"$match": {"case_open": True, "patient_id": userId}},
                {"$group": {
                    "_id": "$caseid",
                    "doctorid": {"$first": "$doctorid"},
                    "doctor_name": {"$first": "$doctor_name"}
                }}
            ]
            case_list = list(self.mongo_db['Appointments'].aggregate(pipeline))
            for case in case_list:
                active_cases_dict[case.get('_id')] = {
                    'doctor_id': case.get('doctorid'),
                    'doctor_name': case.get('doctor_name'),
                }
            return active_cases_dict, None
        except Exception as e:
            return None, f'Error occurred as {str(e)} while fetching active cases'

    def add_emergency_contact(self, user_id: str, emergency_contact_details: EmergencyContactDetails):
        try:
            emc_exist = self.mongo_db['UserCollection'].find_one({
                'userid': str(user_id),
                '$or': [
                    {'emergency_contacts': {'$elemMatch': {'user_id': str(emergency_contact_details.user_id)}}},
                    {'emergency_contacts': {
                        '$elemMatch': {'emergency_contact_id': str(emergency_contact_details.emergency_contact_id)}}}
                ]
            })
            if emc_exist is not None:
                self.mongo_db['UserCollection'].find_one_and_update(
                    {'userid': str(user_id)},
                    {
                        '$set': {
                            'emergency_contacts.$[elem]': dict(emergency_contact_details)
                        }
                    },
                    array_filters=[{'elem.user_id': str(emergency_contact_details.user_id)}]
                )
            else:
                self.mongo_db['UserCollection'].find_one_and_update({'userid': str(user_id)}, {
                    '$push': {
                        'emergency_contacts': dict(emergency_contact_details)
                    }
                })
        except Exception as e:
            raise Exception(f'Error while adding/updating emergency contact: {str(e)}')

    def remove_emergency_contact(self, user_id: str, id_to_remove: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update(
                {'userid': str(user_id)},
                {
                    '$pull': {
                        'emergency_contacts': {
                            '$or': [
                                {'emergency_contact_id': str(id_to_remove)},
                                {'user_id': str(id_to_remove)}
                            ]
                        }}
                })

        except Exception as e:
            raise Exception(f'Error while removing emergency contact: {str(e)}')

    def add_family_member(self, family_member_data: AddFamilyMember, care_taker_id: str):
        try:
            pt_ctrl = RelativesController(db=self.db, mongo=self.mongo)
            # 1. Strict match in DBUser
            user_exists = pt_ctrl.get_user_by_details(user_data=CreateUserByAdmin(
                firstname=family_member_data.firstname,
                lastname=family_member_data.lastname,
                dob=family_member_data.dob,
                email=family_member_data.email,
                mobile=family_member_data.mobile,
                gender=family_member_data.gender
            ))
            if user_exists:
                care_taker_details = self.__get_patient_details(patientid=care_taker_id)
                user_details = self.__get_patient_details(patientid=user_exists.userid)

                add_both_relatives = pt_ctrl.create_relations_and_relationships(caretaker=care_taker_id,
                                                                                patient=user_exists.userid,
                                                                                relation_of_patient_with_caretaker=family_member_data.relation,
                                                                                caretaker_details=care_taker_details,
                                                                                patient_details=user_details,
                                                                                caretaker_registered=True,
                                                                                patient_registered=True,
                                                                                active_flag=False)

                if add_both_relatives is None:
                    raise Exception('Relations already exist')

                relative_one = add_both_relatives['relative_1'].to_dict()  # caretaker - request made from
                relative_two = add_both_relatives['relative_2'].to_dict()  # relative - request made to

                save_approval_request = dict(
                    request_from=care_taker_id,
                    request_to=user_exists.userid,
                    status='Pending',
                    request_date=datetime.datetime.now(),
                    request_from_details=relative_one,
                    request_to_details=relative_two,
                    request_approved_on=None,
                    request_cancelled_on=None
                )

                self.mongo_db['FamilyApprovalRequests'].insert_one(save_approval_request)

                # Send notification to user
                frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)

                family_member_add = FamilyMemberRequestEvent(relation_id=relative_two['relation_id'],
                                                             relative_one_id=relative_two['relativeid'],
                                                             relative_one_name=relative_two['firstname'],
                                                             relative_one_relation_with_relative_two=relative_two[
                                                                 'relationship'],
                                                             relative_two_id=relative_one['relativeid'],
                                                             relative_two_name=relative_one['firstname'] + ' ' +
                                                                               relative_one[
                                                                                   'lastname'],
                                                             relative_two_relation_with_relative_one=relative_one[
                                                                 'relationship'],
                                                             event_date=datetime.datetime.now(),  # request date
                                                             remarks='None')

                text_local_controller = TextLocalController()
                text_local_controller.send_sms(template_name='FamilyMemberAddRequest',
                                               var_list=[family_member_add.relative_two_name],
                                               numbers=relative_two.get('mobile'))

                frbs_ctrl.family_member_add_request(family_member_event=family_member_add)

                patient_name = user_details['firstname'] + ' ' + user_details['lastname']
                patient_id = user_exists.userid
                patient_dob = user_details['dob']
                patient_gender = user_details['gender']

            else:
                # 3. Match in DBRelatives
                get_patient_details = pt_ctrl.get_relative_by_details(
                    caretaker_id=care_taker_id,
                    relative_data=family_member_data
                )
                if get_patient_details:
                    raise Exception('Family member already exists')
                else:
                    patient_age = get_age_in_years(str(family_member_data.dob))
                    if patient_age > 18:
                        email_exists = self.db.query(DBUser).filter(
                            DBUser.email == family_member_data.email).one_or_none()
                        if email_exists is not None:
                            raise Exception(f"Email {family_member_data.email} already exists")

                        mobile_exists = self.db.query(DBUser).filter(
                            DBUser.mobile == family_member_data.mobile).one_or_none()
                        if mobile_exists is not None:
                            raise Exception(f"Mobile {family_member_data.mobile} already exists")

                        eighteen_years_ago = datetime.datetime.now() - relativedelta(years=18)

                        email_exists = self.db.query(DBRelatives).filter(
                            DBRelatives.email == family_member_data.email,
                            DBRelatives.birthdate <= eighteen_years_ago).all()
                        if len(email_exists) > 0:
                            raise Exception(
                                f"A user with email {family_member_data.email} already exists in another AYOO family account")

                        mobile_exists = self.db.query(DBRelatives).filter(
                            DBRelatives.mobile == family_member_data.mobile,
                            DBRelatives.birthdate <= eighteen_years_ago).all()
                        if len(mobile_exists) > 0:
                            raise Exception(
                                f"A user with mobile {family_member_data.mobile} already exists in another AYOO family account")

                    care_taker_details = self.__get_patient_details(patientid=care_taker_id)

                    relation_of_caretaker_with_patient = None
                    if family_member_data.relation == 'Parent':
                        relation_of_caretaker_with_patient = 'Child'

                    if family_member_data.relation == 'Spouse':
                        relation_of_caretaker_with_patient = 'Spouse'

                    if family_member_data.relation == 'Child':
                        relation_of_caretaker_with_patient = 'Parent'

                    if family_member_data.relation == 'Family':
                        relation_of_caretaker_with_patient = 'Family'

                    if family_member_data.relation == 'Others':
                        relation_of_caretaker_with_patient = 'Others'

                    if relation_of_caretaker_with_patient is None:
                        return None, 'Cannot define relations'

                    patient_details, msg = pt_ctrl.create_relative(
                        caretaker_id=care_taker_id,
                        relative_data=family_member_data,
                        caretaker_data=dict(
                            user_id=care_taker_id,
                            ayoo_id=care_taker_details.get('ayoo_id', ''),
                            firstname=care_taker_details['firstname'],
                            lastname=care_taker_details['lastname'],
                            dob=care_taker_details['dob'],
                            email=care_taker_details['email'],
                            mobile=care_taker_details['mobile'],
                            gender=care_taker_details['gender'],
                            relation=relation_of_caretaker_with_patient
                        ), active_flag=True
                    )
                    if patient_details:
                        relative_one = patient_details['relative_1'].to_dict()
                        relative_two = patient_details['relative_2'].to_dict()

                        patient_name = relative_one['firstname'] + \
                                       ' ' + relative_one['lastname']
                        patient_id = relative_one['relativeid']
                        patient_dob = relative_one['birthdate']
                        patient_gender = relative_one['gender']
                    else:
                        raise Exception(f'Could not record patient\'s data: {msg}')

            # add emergency contact
            if family_member_data.is_emergency_contact:
                self.add_emergency_contact(user_id=str(care_taker_id),
                                           emergency_contact_details=EmergencyContactDetails(
                                               emergency_contact_id=str(uuid.uuid4()),
                                               user_id=patient_id,
                                               full_name=patient_name,
                                               mobile=family_member_data.mobile,
                                               email=family_member_data.email,
                                               relation=family_member_data.relation
                                           ))

            return dict(
                msg='Relative Added',
                patient_name=patient_name,
                patient_id=patient_id,
                patient_dob=patient_dob,
                patient_gender=patient_gender,
                relatin_id_user=relative_one.get('relation_id'),
                relation_id_caretaker=relative_two.get('relation_id')
            )

        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred while adding relative: {str(e)}')

    def update_relative(self, relative_data: UpdateFamilyMember, care_taker_id: str):
        try:
            relative_details: DBRelatives = self.db.query(DBRelatives).filter(DBRelatives.caretaker_id == care_taker_id,
                                                                              DBRelatives.relativeid == relative_data.relative_id).one_or_none()
            if relative_details is None:
                raise Exception('Invalid relative ID')

            relative_details.firstname = relative_data.firstname.title()
            relative_details.lastname = relative_data.lastname.title()
            relative_details.gender = relative_data.gender
            relative_details.email = relative_data.email
            relative_details.mobile = relative_data.mobile

            self.db.commit()
            return dict(
                msg='Relative Updated',
                patient_name=relative_details.firstname + ' ' + relative_details.lastname,
                patient_id=relative_details.relativeid,
                patient_dob=relative_details.birthdate,
                patient_gender=relative_details.gender,
                patient_email=relative_details.email,
                patient_mobile=relative_details.mobile
            )

        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=409, detail=f'Error occurred while updating relative: {str(e)}')

    def verify_caretaker_access_code(self, access_code: str, userid: str):
        code_dict = {}
        if not access_code:
            return None, f'Please enter a valid caretaker access code '
        now = datetime.datetime.now()
        now_in_unix = time.mktime(now.timetuple())
        code_query: DBCaretakerAccessCode = self.db.query(DBCaretakerAccessCode).filter_by(
            unique_code=str(access_code)).one_or_none()
        if not code_query:
            return None, f'Invalid code, please try again'
        resp: DBRelatives = self.db.query(dbmodels.DBRelatives).filter(
            DBRelatives.relation_id == code_query.relation_id).one_or_none()
        relation_id = code_query.relation_id
        generated_for = resp.caretaker_id
        if generated_for != userid:
            return None, f'Unauthorised access, please check the code'
        if float(now_in_unix) > float(code_query.expiry_at):
            return None, f'{access_code} has expired'
        return relation_id, None

    # a script function to import mongo db collection all at once
    def import_mongo_data_at_once(self):
        try:
            import json
            db = self.mongo.get_database("ayoo")
            # print(db)
            collection_names = db.list_collection_names()
            # print(collection_names)

            # Export each collection
            export_messages = []
            for collection_name in collection_names:
                collection_data = list(db[collection_name].find())

                # Specify the output file path
                output_file_path = f"{collection_name}.json"
                # print(output_file_path)

                # Write data to a JSON file
                with open(output_file_path, "w") as json_file:
                    json.dump(collection_data, json_file, default=str)
                # print('exported - ', collection_name)
                export_messages.append(f"Exported {collection_name} to {output_file_path}")

            return {"messages": export_messages}
        except Exception as e:
            # print(f'error - {str(e)}')
            return

    def approve_request_for_added_family_member(self, data: ApproveFamilyMemberAddition, logged_in_user_id: str):
        try:
            pt_ctrl = RelativesController(db=self.db, mongo=self.mongo)

            if data.allow_access is not None:
                # Send notification to user
                frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)
                #print(data)

                relative_details_1: DBRelatives = pt_ctrl.get_relation_detail_by_relation_id(
                    relation_id=data.relation_id_user)
                loggers['logger_manage_family'].info(f"relative_details_1 - {relative_details_1}")
                if relative_details_1 is None:
                    raise Exception('User does not exist in relatives table')

                relative_details_2: DBRelatives = pt_ctrl.get_relation_detail_by_relation_id(
                    relation_id=data.relation_id_caretaker)
                loggers['logger_manage_family'].info(f"relative_details_2 - {relative_details_2}")
                if relative_details_2 is None:
                    raise Exception('Family member does not exist in relatives table')

                if data.allow_access is False:
                    try:
                        self.db.delete(relative_details_1)
                        self.db.delete(relative_details_2)
                        self.db.commit()

                    except Exception as e:
                        self.db.rollback()
                        raise Exception(str(e))

                if data.allow_access:
                    relative_details_1.is_active = data.allow_access
                    relative_details_2.is_active = data.allow_access

                    self.db.commit()

                    loggers['logger_manage_family'].info(
                        f"relative_details_1 active status - {relative_details_1.is_active}")
                    loggers['logger_manage_family'].info(
                        f"relative_details_2 active status - {relative_details_2.is_active}")

                request_status = 'Approved' if data.allow_access is True else 'Cancelled'

                updated_record = self.mongo_db['FamilyApprovalRequests'].find_one_and_update({
                    '$or': [{
                        '$and': [{'request_to': logged_in_user_id},
                                 {'request_from': relative_details_2.relativeid},
                                 {'status': 'Pending'}]
                    }, {
                        '$and': [{'request_to': relative_details_2.relativeid},
                                 {'request_from': logged_in_user_id},
                                 {'status': 'Pending'}]
                    }]}, {
                    '$set': {
                        'status': request_status,
                        f'request_{request_status.lower()}_on': datetime.datetime.now()
                    }}, return_document=pymongo.ReturnDocument.AFTER)

                family_add_status = True if data.allow_access is True else False
                loggers['logger_manage_family'].info(f"family_add_status - {family_add_status}")

                family_member_add = FamilyMemberAddEvent(family_id=str(relative_details_1.relation_id),
                                                         relative_one_id=relative_details_1.relativeid,
                                                         relative_one_name=relative_details_1.firstname + ' ' + relative_details_1.lastname,
                                                         relative_one_relation_with_relative_two=relative_details_2.relationship,
                                                         relative_two_id=relative_details_2.relativeid,
                                                         relative_two_name=relative_details_2.firstname + ' ' + relative_details_2.lastname,
                                                         is_member=family_add_status,
                                                         relative_two_relation_with_relative_one=relative_details_1.relationship,
                                                         event_date=datetime.datetime.now(),  # add or remove date
                                                         remarks='None')
                text_local_controller = TextLocalController()
                if family_add_status is True:
                    text_local_controller.send_sms(template_name='FamilyMemberAddRelativeOne',
                                                   var_list=[family_member_add.relative_one_name],
                                                   numbers=relative_details_2.mobile)
                    text_local_controller.send_sms(template_name='FamilyMemberAddRelativeTwo',
                                                   var_list=[family_member_add.relative_one_name,
                                                             family_member_add.relative_two_name],
                                                   numbers=relative_details_1.mobile)
                    frbs_ctrl.family_member_add(family_member_event=family_member_add)

                else:
                    print('access not allowed')
                    frbs_ctrl.family_member_remove(family_member_event=family_member_add)

                    text_local_controller.send_sms(template_name='FamilyMemberRemoveRelativeOne',
                                                   var_list=[family_member_add.relative_two_name],
                                                   numbers=relative_details_1.mobile)
                    text_local_controller.send_sms(template_name='FamilyMemberRemoveRelativeTwo',
                                                   var_list=[family_member_add.relative_one_name],
                                                   numbers=relative_details_2.mobile)

                if updated_record is None:
                    raise Exception('No record updated as data not found.')

                return dict(request_from=updated_record['request_from'],
                            request_to=updated_record['request_to'],
                            status=updated_record['status'],
                            request_date=updated_record['request_date'],
                            request_from_details=updated_record['request_from_details'],
                            request_to_details=updated_record['request_to_details'],
                            request_approved_on=updated_record['request_approved_on'],
                            request_cancelled_on=updated_record['request_cancelled_on']
                            )

            else:
                pending_approvals = self.mongo_db['FamilyApprovalRequests'].find({
                    '$and': [
                        {'request_to': logged_in_user_id},
                        {'status': 'Pending'}]
                })
                all_records = []
                for record in list(pending_approvals.clone()):
                    all_records.append(dict(request_from=record['request_from'],
                                            request_to=record['request_to'],
                                            status=record['status'],
                                            request_date=record['request_date'],
                                            request_from_details=record['request_from_details'],
                                            request_to_details=record['request_to_details'],
                                            request_approved_on=record['request_approved_on'],
                                            request_cancelled_on=record['request_cancelled_on']
                                            ))

                return all_records
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=409, detail=f'Error occurred while adding relative: {str(e)}')

    def update_consent_records(self, data: ConsentRecord):
        try:
            user_mongo_data = self.mongo_db['UserConsentRecord'].find_one({'relation_id': data.relation_id})

            if user_mongo_data is None:
                consent_starts_on = datetime.datetime.now()
                consent_ends_on = consent_starts_on + datetime.timedelta(days=int(data.consent_duration))
                self.mongo_db['UserConsentRecord'].insert_one(
                    dict(
                        relation_id=data.relation_id,
                        consent_duration=data.consent_duration,
                        consent_starts_on=consent_starts_on,
                        consent_ends_on=consent_ends_on
                    )
                )
            else:
                self.mongo_db['UserConsentRecord'].find_one_and_update({'relation_id': data.relation_id},
                                                                       {'$set': {
                                                                           'consent_duration': data.consent_duration,
                                                                           'consent_starts_on': datetime.datetime.now(),
                                                                           'consent_ends_on': datetime.datetime.now() + datetime.timedelta(
                                                                               days=int(data.consent_duration))}})
            return True, 'Caretaker Access Updated'
        except Exception as e:
            return None, f'{str(e)}'

    def delete_family_relation(self, data: DeleteFamilyRelation):
        try:
            related_relation_id = self.related_realtion_id(data.relation_id)
            if related_relation_id:
                relation_to_delete_1: DBRelatives = self.db.query(DBRelatives).filter_by(
                    relation_id=data.relation_id).first()
                relation_to_delete_2: DBRelatives = self.db.query(DBRelatives).filter_by(
                    relation_id=related_relation_id).first()
                try:
                    pt_ctrl = RelativesController(db=self.db, mongo=self.mongo)
                    user_one = CreateUserByAdmin(firstname=relation_to_delete_1.firstname,
                                                 lastname=relation_to_delete_1.lastname,
                                                 dob=str(relation_to_delete_1.birthdate),
                                                 email=relation_to_delete_1.email,
                                                 mobile=relation_to_delete_1.mobile,
                                                 gender=relation_to_delete_1.gender)
                    user_two = CreateUserByAdmin(firstname=relation_to_delete_2.firstname,
                                                 lastname=relation_to_delete_2.lastname,
                                                 dob=str(relation_to_delete_2.birthdate),
                                                 email=relation_to_delete_2.email,
                                                 mobile=relation_to_delete_2.mobile,
                                                 gender=relation_to_delete_2.gender)
                    add_relative = True
                    msg = None
                    if relation_to_delete_1.is_registered is False:
                        add_relative, msg = pt_ctrl.create_user(user_data=user_one,
                                                                existing_uuid=relation_to_delete_1.relativeid,
                                                                is_registered=False,
                                                                exisiting_ayoo_id=relation_to_delete_1.ayoo_id,
                                                                joining_date=relation_to_delete_1.date_created)

                    if relation_to_delete_2.is_registered is False:
                        add_relative, msg = pt_ctrl.create_user(user_data=user_two,
                                                                existing_uuid=relation_to_delete_2.relativeid,
                                                                is_registered=False,
                                                                exisiting_ayoo_id=relation_to_delete_2.ayoo_id,
                                                                joining_date=relation_to_delete_2.date_created)

                    if add_relative is None:
                        raise Exception(msg)

                    # delete emergency contacts:
                    self.remove_emergency_contact(user_id=str(relation_to_delete_1.relativeid),
                                                  id_to_remove=str(relation_to_delete_2.relativeid))
                    self.remove_emergency_contact(user_id=str(relation_to_delete_2.relativeid),
                                                  id_to_remove=str(relation_to_delete_1.relativeid))

                    self.db.delete(relation_to_delete_1)
                    self.db.delete(relation_to_delete_2)
                    self.db.commit()
                except Exception as e:
                    self.db.rollback()
                    raise HTTPException(status_code=409, detail=f'Error occurred while deleting relative: {str(e)}')

                criteria = {
                    '$or': [
                        {
                            '$and': [
                                {'request_to': relation_to_delete_1.relativeid},
                                {'request_from': relation_to_delete_2.relativeid},
                                {'status': 'Pending'}
                            ]
                        },
                        {
                            '$and': [
                                {'request_to': relation_to_delete_2.relativeid},
                                {'request_from': relation_to_delete_1.relativeid},
                                {'status': 'Pending'}
                            ]
                        }
                    ]
                }
                update_operation = {
                    '$set': {
                        'status': 'Deleted'
                    }
                }
                updated_count = self.mongo_db['FamilyApprovalRequests'].update_many(criteria, update_operation)
                frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)
                family_member_add = FamilyMemberAddEvent(family_id=str(relation_to_delete_1.relation_id),
                                                         relative_one_id=relation_to_delete_1.relativeid,
                                                         relative_one_name=relation_to_delete_1.firstname + ' ' + relation_to_delete_1.lastname,
                                                         relative_one_relation_with_relative_two=relation_to_delete_2.relationship,
                                                         relative_two_id=relation_to_delete_2.relativeid,
                                                         relative_two_name=relation_to_delete_2.firstname + ' ' + relation_to_delete_2.lastname,
                                                         is_member=False,
                                                         relative_two_relation_with_relative_one=relation_to_delete_1.relationship,
                                                         event_date=datetime.datetime.now(),  # add or remove date
                                                         remarks='None')

                text_local_controller = TextLocalController()
                text_local_controller.send_sms(template_name='FamilyMemberRemoveRelativeTwo',
                                               var_list=[family_member_add.relative_two_name],
                                               numbers=relation_to_delete_1.mobile)
                text_local_controller.send_sms(template_name='FamilyMemberRemoveRelativeOne',
                                               var_list=[family_member_add.relative_one_name],
                                               numbers=relation_to_delete_2.mobile)

                frbs_ctrl.family_member_remove(family_member_event=family_member_add)
                return True, 'Relation successfully deleted'
            else:
                return False, 'Related relation not found'
        except Exception as e:
            return None, f'{str(e)}'

    def logout(self, user_id, data: LogoutRequest):
        try:
            device_collection = self.mongo_db["UserDeviceInfo"]
            if data and data.device_id:
                device_collection.delete_one({"user_id": user_id, "device_id": data.device_id, "user_type": "User"})
            # To Do Invalidate user token and other settings if any
            return {f'User Logged out'}, None

        except Exception as e:
            return None, f'{str(e)}'

    def merge_medical_history(self, user_id):
        try:
            records = list(self.mongo_db['UserCollection'].find({"userid": user_id}).clone())

            if len(records) < 2:
                return

            combined_medical_history = {
                "vitals": [],
                "known_allergies": [],
                "prior_medical_history": [],
                "current_or_past_medication": [],
                "family_history": [],
                "vaccination_record": [],
                "obstetric_history": []
            }

            for record in records:
                medical_history = record.get('medical_history', {})
                for key, value in medical_history.items():
                    combined_medical_history[key].extend(value)

            for key, value in combined_medical_history.items():
                combined_medical_history[key] = sorted(
                    value,
                    key=lambda x: x['created_at'] if isinstance(x['created_at'], datetime.datetime)
                    else x['created_at'].get('$date', datetime.datetime.min)
                )

            self.mongo_db['UserCollection'].update_one(
                {"_id": records[0]["_id"]},
                {"$set": {"medical_history": combined_medical_history}}
            )

            ids_to_delete = [record["_id"] for record in records[1:]]
            self.mongo_db['UserCollection'].delete_many({"_id": {"$in": ids_to_delete}})

            return {"status": "success", "message": "Medical history merged and records updated."}

        except Exception as e:
            raise Exception(f"Error merging medical history: {str(e)}")

    def merge_patient_profiles(self, patient_data: PatientProfileMerge):
        try:
            # Validation 1: Check if all provided user IDs exist
            users_info = self.db.query(DBUser).filter(
                DBUser.userid.in_(patient_data.user_ids)).all()

            found_user_ids = [user.userid for user in users_info]
            missing_user_ids = [user_id for user_id in patient_data.user_ids if user_id not in found_user_ids]

            # Check if missing users exist in relatives table
            if missing_user_ids:
                relatives_info = self.db.query(DBRelatives).filter(
                    DBRelatives.relativeid.in_(missing_user_ids)).all()

                found_relative_ids = [relative.relativeid for relative in relatives_info]
                still_missing_ids = [user_id for user_id in missing_user_ids if user_id not in found_relative_ids]

                if still_missing_ids:
                    raise Exception(f"Invalid user IDs: {', '.join(still_missing_ids)}")

            # Get all profiles together for validation
            all_profiles = []

            # Add users to profiles
            for user in users_info:
                all_profiles.append({
                    'id': user.userid,
                    'firstname': user.firstname,
                    'lastname': user.lastname,
                    'gender': user.gender,
                    'birthdate': str(user.birthdate)
                })

            # Add relatives to profiles if they're not already in users
            relatives_info = self.db.query(DBRelatives).filter(
                DBRelatives.relativeid.in_(patient_data.user_ids)).all()

            for relative in relatives_info:
                if relative.relativeid not in found_user_ids:
                    all_profiles.append({
                        'id': relative.relativeid,
                        'firstname': relative.firstname,
                        'lastname': relative.lastname,
                        'gender': relative.gender,
                        'birthdate': str(relative.birthdate)
                    })

            # Validation 2: Check if first letter of first name, last name, gender, and age match
            if len(all_profiles) > 1:
                first_profile = all_profiles[0]
                first_name_first_letter = first_profile['firstname'].strip()[0].lower() if first_profile['firstname'] else None
                last_name_first_letter = first_profile['lastname'].strip()[0].lower() if first_profile['lastname'] else None
                gender = first_profile['gender'].strip()
                age = get_age_in_years(first_profile['birthdate'].strip())

                for profile in all_profiles[1:]:
                    # Check first letter of first name
                    if not profile['firstname'] or profile['firstname'].strip()[0].lower() != first_name_first_letter:
                        raise Exception("First letter of first name doesn't match")

                    # Check first letter of last name
                    if not profile['lastname'] or profile['lastname'].strip()[0].lower() != last_name_first_letter:
                        raise Exception("First letter of last name doesn't match")

                    # Check gender
                    if profile['gender'].strip() != gender:
                        raise Exception("Gender doesn't match for profile")

                    # Check age
                    profile_age = get_age_in_years(profile['birthdate'].strip())
                    if profile_age != age:
                        raise Exception("Age doesn't match for profile")

            # Sort users by joining date for the merge process
            users_info = sorted(users_info, key=lambda user: user.joining_date)

            pt_ctrl = RelativesController(db=self.db, mongo=self.mongo)
            if not users_info:
                relatives_info = self.db.query(DBRelatives).filter(
                    DBRelatives.relativeid.in_(patient_data.user_ids)).order_by(DBRelatives.date_created).all()
                for _relative in relatives_info:
                    _relative_data: DBRelatives = _relative

                    register_user = CreateUserByAdmin(
                        firstname=_relative_data.firstname,
                        lastname=_relative_data.lastname,
                        dob=str(_relative_data.birthdate),
                        email=_relative_data.email,
                        mobile=_relative_data.mobile,
                        gender=_relative_data.gender
                    )
                    add_relative, msg = pt_ctrl.create_user(
                        user_data=register_user,
                        existing_uuid=_relative_data.relativeid,
                        exisiting_ayoo_id=_relative_data.ayoo_id,
                        joining_date=_relative_data.date_created,
                        is_registered=False)

                users_info = self.db.query(DBUser).filter(
                    DBUser.userid.in_(patient_data.user_ids)).order_by(
                    DBUser.joining_date).all()
            user: DBUser = users_info[0]

            for index, other_user in enumerate(users_info):
                #print('index: ', index)
                if index == 0:
                    continue

                deleted_user: DBUser = other_user
                deleted_user.firstname = ''
                deleted_user.lastname = ''
                deleted_user.email = ''
                deleted_user.mobile = ''
                deleted_user.enc_password = '--'
                deleted_user.is_active = False
                deleted_user.is_registered = False
                deleted_user.is_deleted = True
                deleted_user.date_registered = None
                deleted_user.date_deleted = datetime.datetime.now()

            all_relatives = self.db.query(DBRelatives).filter(
                DBRelatives.relativeid.in_(patient_data.user_ids)
            ).all()
            for _rel in all_relatives:
                updt_rel: DBRelatives = _rel
                updt_rel.relativeid = str(user.userid)
                updt_rel.firstname = patient_data.firstname
                updt_rel.lastname = patient_data.lastname
                updt_rel.mobile = patient_data.mobile
                updt_rel.email = patient_data.email

            all_relatives = self.db.query(DBRelatives).filter(
                DBRelatives.caretaker_id.in_(patient_data.user_ids)
            ).all()
            for _rel in all_relatives:
                updt_rel: DBRelatives = _rel
                updt_rel.caretaker_id = str(user.userid)

            query = {"patient_id": {"$in": patient_data.user_ids}}

            pipeline = [
                {"$match": query},
                {"$sort": {"appointment_slot": -1}},
                {
                    "$group": {
                        "_id": {
                            "patient_id": "$patient_id",
                            "doctorid": "$doctorid"
                        },
                        "latest_case": {"$first": "$$ROOT"}
                    }
                },
                {
                    "$replaceRoot": {"newRoot": "$latest_case"}
                },
                {"$sort": {"appointment_slot": -1}}
            ]

            all_cases = list(self.mongo_db['Appointments'].aggregate(pipeline))
            latest_cases = {}
            doctor_cases = {}

            for case in all_cases:
                doctor_id = case["doctorid"]
                doctor_name = case["doctor_name"]
                case_id = case.get('caseid')
                appointment_time = case["appointment_slot"]

                if doctor_id not in latest_cases or appointment_time > latest_cases[doctor_id]["appointment_time"]:
                    latest_cases[doctor_id] = {"caseid": case["caseid"], "doctor_name": case["doctor_name"],
                                               "appointment_time": appointment_time}

                if doctor_id and case_id:
                    if doctor_id not in doctor_cases:
                        doctor_cases[doctor_id] = {
                            'doctor_id': doctor_id,
                            'doctor_name': doctor_name,
                            'cases': []
                        }
                    doctor_cases[doctor_id]['cases'].append(case_id)

            for doc_id, data in doctor_cases.items():
                # Get all closed cases for this doctor
                closed_cases = list(self.mongo_db['CaseSheet'].find(
                    {
                        'patient_id': {'$in': patient_data.user_ids},
                        'case_doctor': doc_id,
                        'is_open': False
                    },
                    {'case_id': 1}
                ))
                closed_case_ids = [case['case_id'] for case in closed_cases]

                # Update ReferCase for closed cases - only change patient_id
                self.mongo_db['ReferCase'].update_many({
                    'patient_id': {'$in': patient_data.user_ids},
                    'referred_by': doc_id,
                    'existing_case_id': {'$in': closed_case_ids}
                }, {
                    '$set': {
                        'patient_id': user.userid
                    }
                })

                # Update ReferCase for open cases - change both patient_id and existing_case_id
                self.mongo_db['ReferCase'].update_many({
                    'patient_id': {'$in': patient_data.user_ids},
                    'referred_by': doc_id,
                    'existing_case_id': {'$nin': closed_case_ids},
                    'existing_case_id': {'$in': data['cases']}
                }, {
                    '$set': {
                        'patient_id': user.userid,
                        'existing_case_id': latest_cases.get(doc_id, {}).get('caseid')
                    }
                })

                # Update ReferCase for closed cases - only change patient_id
                self.mongo_db['ReferCase'].update_many({
                    'patient_id': {'$in': patient_data.user_ids},
                    'referred_to': doc_id,
                    'referred_case_id': {'$in': closed_case_ids}
                }, {
                    '$set': {
                        'patient_id': user.userid
                    }
                })

                # Update ReferCase for open cases - change both patient_id and referred_case_id
                self.mongo_db['ReferCase'].update_many({
                    'patient_id': {'$in': patient_data.user_ids},
                    'referred_to': doc_id,
                    'referred_case_id': {'$nin': closed_case_ids}
                }, {
                    '$set': {
                        'patient_id': user.userid,
                        'referred_case_id': latest_cases.get(doc_id, {}).get('caseid')
                    }
                })

            all_docs = list(self.mongo_db['ReferCase'].find().clone())

            grouped = defaultdict(list)
            for doc in all_docs:
                key = (
                    doc.get("patient_id"),
                    doc.get("existing_case_id"),
                    doc.get("referred_case_id"),
                    doc.get("referral_type"),
                    doc.get("referred_by"),
                    doc.get("referred_to")
                )
                grouped[key].append(doc)

            for key, docs in grouped.items():
                if len(docs) > 1:
                    merged_updates = []
                    seen_note_ids = set()

                    for d in docs:
                        for upd in d.get("updates", []):
                            note_id = upd.get("note_id")
                            if note_id not in seen_note_ids:
                                merged_updates.append(upd)
                                seen_note_ids.add(note_id)

                    merged_updates.sort(key=lambda x: x.get("date_recorded"), reverse=True)
                    doc_to_keep = docs[0]
                    self.mongo_db['ReferCase'].update_one(
                        {"_id": doc_to_keep["_id"]},
                        {"$set": {"updates": merged_updates}}
                    )
                    for doc_to_delete in docs[1:]:
                        self.mongo_db['ReferCase'].delete_one({"_id": doc_to_delete["_id"]})

            latest_case_list = [{"doctorid": doc_id, "caseid": data["caseid"], "doctor_name": data["doctor_name"]} for
                                doc_id, data in latest_cases.items()]

            for record in latest_case_list:
                latest_case = record.get('caseid')
                doctor_id = record.get('doctorid')

                # Get all closed cases for this doctor
                closed_cases = list(self.mongo_db['CaseSheet'].find(
                    {
                        'patient_id': {'$in': patient_data.user_ids},
                        'case_doctor': doctor_id,
                        'is_open': False
                    },
                    {'case_id': 1}
                ))
                closed_case_ids = [case['case_id'] for case in closed_cases]

                # Update closed cases (case_open=False) - only change patient_id
                self.mongo_db['Appointments'].update_many(
                    {
                        'patient_id': {'$in': patient_data.user_ids},
                        'doctorid': doctor_id,
                        'case_open': False  # Only update closed cases
                    },
                    {
                        '$set': {
                            'patient_id': user.userid
                        }
                    }
                )

                # Update open cases (case_open=True) - change both patient_id and caseid
                self.mongo_db['Appointments'].update_many(
                    {
                        'patient_id': {'$in': patient_data.user_ids},
                        'doctorid': doctor_id,
                        'case_open': True  # Only update open cases
                    },
                    {
                        '$set': {
                            'patient_id': user.userid,
                            'caseid': latest_case
                        }
                    }
                )

                # Also updating booked_by field to ensure family member appointments are properly retained
                self.mongo_db['Appointments'].update_many({'booked_by': {'$in': patient_data.user_ids}}, {
                    '$set': {
                        'booked_by': user.userid
                    }
                })

                # 1. Get all case sheets for doctor-user combo and sort by appointment slot and prescription count
                all_case_sheets = list(self.mongo_db['CaseSheet'].find(
                    {'patient_id': {'$in': patient_data.user_ids}, 'case_doctor': doctor_id}
                ).sort([('appointment_slot', -1), ('prescription_count', -1)]))

                # 2. Find the first element after sort and store its case_sheet_id and edit flag
                first_case_sheet_id = None
                first_case_sheet_edit_mode = False
                if all_case_sheets:
                    first_case_sheet_id = all_case_sheets[0].get('case_sheet_id')
                    first_case_sheet_appointment_id = all_case_sheets[0].get('appointment_id')
                    first_case_sheet_edit_mode = all_case_sheets[0].get('case_sheet_in_edit_mode', False)

                # 3. Merge all casesheets
                # Update only patient_id for closed cases (is_open=False)
                self.mongo_db['CaseSheet'].update_many(
                    {
                        'patient_id': {'$in': patient_data.user_ids},
                        '$or': [
                            {'case_doctor': doctor_id},
                            {'case_doctor': {'$exists': False}},
                            {'case_doctor': None}
                        ],
                        'is_open': False  # Only update closed cases
                    },
                    {
                        '$set': {
                            'patient_id': user.userid,
                            'case_sheet_in_edit_mode': False  # Setting all to false, as the first one will be updated later
                        }
                    }
                )

                # Update both patient_id and case_id for open cases (is_open=True)
                self.mongo_db['CaseSheet'].update_many(
                    {
                        'patient_id': {'$in': patient_data.user_ids},
                        '$or': [
                            {'case_doctor': doctor_id},
                            {'case_doctor': {'$exists': False}},
                            {'case_doctor': None}
                        ],
                        'is_open': True  # Only update open cases
                    },
                    {
                        '$set': {
                            'patient_id': user.userid,
                            'case_id': latest_case,
                            'case_sheet_in_edit_mode': False  # Setting all to false, as the first one will be updated later
                        }
                    }
                )

                # 4. Restore the original edit flag for the first case sheet with the stored case_sheet_id
                if first_case_sheet_id:
                    self.mongo_db['CaseSheet'].update_one(
                        {'case_sheet_id': first_case_sheet_id},
                        {'$set': {'case_sheet_in_edit_mode': first_case_sheet_edit_mode}}  # Restoring the original edit flag
                    )

                    case_sheet_dao = CaseSheetDAO()

                    # Get all case sheets for this doctor and patient except the first one
                    other_case_sheets = list(self.mongo_db['CaseSheet'].find(
                        {
                            'patient_id': user.userid,
                            'case_doctor': doctor_id,
                            'appointment_id': {'$ne': first_case_sheet_appointment_id},
                            'prescription_s3_object_key': {'$ne': None},  
                            'is_open': True 
                        }
                    ))

                    # hash map for tracking processed appointment id
                    processed_appointment_ids = set()

                    # Apply expired watermark to all other prescriptions
                    if len(other_case_sheets) > 0:
                        for case_sheet in other_case_sheets:
                            appointment_id = case_sheet.get('appointment_id')
                            if appointment_id in processed_appointment_ids:
                                continue
                            processed_appointment_ids.add(appointment_id)
                            if case_sheet.get('prescription_s3_object_key'):
                                case_sheet_dao.expire_current_prescription(appointment_id=appointment_id)

                # Update Notifications for closed cases - only change user IDs
                self.mongo_db['Notifications'].update_many(
                    {
                        'sent_to': {'$in': patient_data.user_ids},
                        'metadata.caseid': {'$in': closed_case_ids}
                    },
                    {
                        '$set': {
                            'sent_to': user.userid,
                            'metadata.patient.ayoo_id': user.userid
                        }
                    }
                )

                # Update Notifications for open cases - change both user IDs and case ID
                self.mongo_db['Notifications'].update_many(
                    {
                        'sent_to': {'$in': patient_data.user_ids},
                        'metadata.caseid': {'$nin': closed_case_ids}
                    },
                    {
                        '$set': {
                            'sent_to': user.userid,
                            'metadata.patient.ayoo_id': user.userid,
                            'metadata.caseid': latest_case
                        }
                    }
                )

                self.mongo_db['PatientReports'].update_many({'patient_id': {'$in': patient_data.user_ids}}, {
                    '$set': {
                        'patient_id': user.userid,

                    }
                })

                doctor_case_ids = doctor_cases.get(doctor_id, {}).get('cases', [])

                # Update UserLocker documents for this doctor's open cases - change both userid and caseId
                self.mongo_db['UserLocker'].update_many(
                    {
                        'userid': {'$in': patient_data.user_ids},
                        '$and': [
                            {'caseId': {'$in': doctor_case_ids}},  # Only update documents for this doctor's cases
                            {'caseId': {'$nin': closed_case_ids}}  # Exclude closed cases
                        ]
                    },
                    {
                        '$set': {
                            'userid': user.userid,
                            'caseId': latest_case
                        }
                    }
                )

            self.mongo_db['ChatMessages'].update_many({'user_two_id': {'$in': patient_data.user_ids}}, {
                '$set': {
                    'user_two_id': user.userid
                }
            })

            self.mongo_db['FamilyApprovalRequests'].update_many({'request_from': {'$in': patient_data.user_ids}}, {
                '$set': {
                    'request_from': user.userid
                }
            })
            self.mongo_db['FamilyApprovalRequests'].update_many({'request_to': {'$in': patient_data.user_ids}}, {
                '$set': {
                    'request_to': user.userid
                }
            })

            self.mongo_db['HASession'].update_many({'user_id': {'$in': patient_data.user_ids}}, {
                '$set': {
                    'user_id': user.userid
                }
            })

            self.mongo_db['JitsiMeetInfo'].update_many({'patient_id': {'$in': patient_data.user_ids}}, {
                '$set': {
                    'patient_id': user.userid,
                    'patient_details.patient_id': user.userid
                }
            })

            self.mongo_db['PatientFeedbacks'].update_many({'patientid': {'$in': patient_data.user_ids}}, {
                '$set': {
                    'patientid': user.userid,
                }
            })

            self.mongo_db['Paymentgateway3'].update_many({'userid': {'$in': patient_data.user_ids}}, {
                '$set': {
                    'userid': user.userid,
                }
            })

            self.mongo_db['UserCollection'].update_many({'userid': {'$in': patient_data.user_ids}}, {
                '$set': {
                    'userid': user.userid,
                }
            })

            self.merge_medical_history(user_id=user.userid)

            self.mongo_db['UserDeviceInfo'].update_many({'user_id': {'$in': patient_data.user_ids}}, {
                '$set': {
                    'user_id': user.userid,
                }
            })

            self.mongo_db['UserLocker'].update_many({'userid': {'$in': patient_data.user_ids}}, {
                '$set': {
                    'userid': user.userid,
                }
            })

            self.mongo_db['user-vitals'].update_many({'user_id': {'$in': patient_data.user_ids}}, {
                '$set': {
                    'user_id': user.userid,
                }
            })

            self.db.commit()


            # Update custom fees to retain special pricing
            custom_fees_dao = CustomFeesDAO()
            for other_user_id in patient_data.user_ids:
                if other_user_id != user.userid:
                    # Find all custom fees for this user
                    custom_fees = self.db.query(custom_fees_dao._model_class).filter(
                        custom_fees_dao._model_class.patient_id == other_user_id
                    ).all()
                    # Update each custom fee to use the main user ID
                    for fee in custom_fees:
                        fee.patient_id = user.userid

            self.db.commit()
            user.firstname = patient_data.firstname
            user.lastname = patient_data.lastname
            user.mobile = patient_data.mobile
            user.email = patient_data.email

            self.db.commit()

            # Appointments - booked_by, patient_id, case_id
            # CaseSheet - patient_id, case_id, session_no
            # ChatMessages - user_two_id
            # FamilyApprovalRequests - request_from, request_to
            # HASession - user_id
            # JitsiMeetInfo - patient_id, patient_details.patient_id
            # Notifications - sent_to, metadata.caseid, metadata.patient.ayoo_id
            # PatientFeedbacks - patientid
            # PatientReports - patient_id, case_id
            # Paymentgateway3 - userid
            # ReferCase - patient_id, existing_case_id, referred_case_id # =======TO LOOK CAREFULLY FOR THE CASE ID======
            # UserCollection - userid
            # UserConsentRecord - relation_id
            # UserDeviceInfo - user_id
            # UserLocker - userid
            # user-vitals - user_id

            ###################################
            ## remove duplicate relations
            duplicates = (
                self.db.query(DBRelatives.caretaker_id, DBRelatives.relativeid,
                              func.min(DBRelatives.relation_id).label("min_id"))
                .group_by(DBRelatives.caretaker_id, DBRelatives.relativeid)
                .subquery()
            )

            # Step 2: Delete duplicates while keeping the earliest relation_id
            self.db.query(DBRelatives).filter(
                and_(
                    DBRelatives.caretaker_id == duplicates.c.caretaker_id,
                    DBRelatives.relativeid == duplicates.c.relativeid,
                    DBRelatives.relation_id != duplicates.c.min_id
                )
            ).delete(synchronize_session=False)

            # Commit the changes
            self.db.commit()

            ################################################################################
            ################################################################################
            ################################################################################
            ################################################################################

            distinct_case_ids = self.mongo_db['CaseSheet'].distinct('case_id', {'patient_id': user.userid})
            for d_case_id in distinct_case_ids:

                all_case_sheets = list(
                    self.mongo_db['CaseSheet'].find({'patient_id': user.userid, 'case_id': d_case_id}).sort(
                        [("appointment_slot", 1), ("created_at", 1)]))

                updated_cases = []

                # Group by doctor_id and appointment_id
                appointment_groups = {}
                for case in all_case_sheets:
                    if case.get('is_open'):
                        doctor_id = case['case_doctor']

                        if doctor_id not in appointment_groups:
                            appointment_groups[doctor_id] = {}

                        # Group by appointment_id
                        appointment_id = case['appointment_id']
                        if appointment_id not in appointment_groups[doctor_id]:
                            appointment_groups[doctor_id][appointment_id] = []

                        appointment_groups[doctor_id][appointment_id].append(case)

                # Process each doctor's cases
                for doctor_id, appointment_dict in appointment_groups.items():
                    # Get unique appointment IDs and sort them by appointment slot
                    appointment_ids = []
                    appointment_slots = {}

                    for appointment_id, cases in appointment_dict.items():
                        # Use the earliest appointment slot for sorting
                        earliest_case = min(cases, key=lambda x: x['appointment_slot'])
                        appointment_slots[appointment_id] = earliest_case['appointment_slot']
                        appointment_ids.append(appointment_id)

                    # Sort appointment IDs by their appointment slots
                    appointment_ids.sort(key=lambda appt_id: appointment_slots[appt_id])

                    # Assign session numbers based on unique appointment IDs
                    for i, appointment_id in enumerate(appointment_ids, 1):
                        session_no = i
                        cases = appointment_dict[appointment_id]

                        # Sort cases within the same appointment ID by prescription count
                        cases.sort(key=lambda x: x.get('prescription_count', 1))

                        # Assign the same session number to all cases with the same appointment ID
                        for j, case in enumerate(cases, 1):
                            prescription_count = j

                            updated_cases.append({
                                "_id": case["_id"],
                                "session_no": session_no,
                                "prescription_count": prescription_count
                            })

                # Update all cases with their new session and prescription numbers
                for case in updated_cases:
                    # First update the CaseSheet with new session and prescription numbers
                    self.mongo_db['CaseSheet'].update_one(
                        {"_id": case["_id"]},
                        {"$set": {"session_no": case["session_no"], "prescription_count": case["prescription_count"]}}
                    )

                    # Find the corresponding CaseSheet document to get appointment_id and case_id
                    case_sheet = self.mongo_db['CaseSheet'].find_one({"_id": case["_id"]})
                    if case_sheet and case_sheet.get("appointment_id") and case_sheet.get("case_id"):
                        # Update only the appointment_no for all lab tests, preserving the original prescription_no
                        self.mongo_db['UserLocker'].update_many(
                            {
                                "userid": user.userid,
                                "caseId": case_sheet.get("case_id"),
                                "fileTag": "578f236d-4533-4a48-b6d3-dbfbf8543823",  # Lab tests file tag
                                "appointment_id": case_sheet.get("appointment_id")  # Filter by appointment_id
                            },
                            {
                                "$set": {
                                    "appointment_no": case["session_no"]
                                }
                            }
                        )

            return {
                'msg': 'Profiles merged'
            }

        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=409, detail=f'Merge profile: {str(e)}')
